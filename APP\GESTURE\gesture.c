#include "../APP/GESTURE/gesture.h"

//////////////////////////////////////////////////////////////////////////////////	 
// 非接触式控制盘 - 手势识别模块实现
//////////////////////////////////////////////////////////////////////////////////

// 全局变量定义
GestureStatus_t gesture_status;
SlideDetection_t slide_detection;

// 静态函数声明
static GestureType_t Gesture_ClassifyGesture(PhotoSwitch_t first, PhotoSwitch_t second);

// 手势名称字符串
static const char* gesture_names[GESTURE_COUNT] = {
    "NONE",
    "S1->S2",
    "S2->S1",
    "S3->S4",
    "S4->S3",
    "S1->S4",
    "S4->S1",
    "S4->S2",
    "S3->S1",
    "S3->S2"
};

/**
 * @brief  手势识别初始化
 * @param  None
 * @retval None
 */
void Gesture_Init(void)
{
    gesture_status.first_switch = SWITCH_COUNT;
    gesture_status.second_switch = SWITCH_COUNT;
    gesture_status.first_time = 0;
    gesture_status.second_time = 0;
    gesture_status.gesture_active = 0;
    gesture_status.last_gesture = GESTURE_NONE;

    // 初始化滑动检测结构体
    slide_detection.last_result = SLIDE_RESULT_NONE;
    slide_detection.invalid_count = 0;
    slide_detection.last_invalid_time = 0;
}

/**
 * @brief  手势识别主函数 - 核心算法实现
 * @param  None
 * @retval 识别到的手势类型
 *
 * 【算法流程】
 * 1. 检查手势识别超时（防止单个开关触发后长时间无后续动作）
 * 2. 扫描所有光电开关的状态变化
 * 3. 记录第一个和第二个开关的触发时间
 * 4. 验证时间间隔是否在有效范围内（50-800ms）
 * 5. 根据开关组合分类识别手势类型
 * 6. 返回识别结果并重置状态机
 */
GestureType_t Gesture_Recognize(void)
{
    uint32_t current_time = HAL_GetTick();
    GestureType_t detected_gesture = GESTURE_NONE;
    PhotoSwitch_t sw;
    // 检查手势识别超时（1000ms）
    // 如果第一个开关触发后1秒内没有第二个开关触发，则认为是误操作
    if(gesture_status.gesture_active &&
       (current_time - gesture_status.first_time > GESTURE_TIMEOUT_MS))
    {
        Gesture_Reset();  // 重置状态机，准备下次识别
        return GESTURE_NONE;
    }
    
    // 扫描所有光电开关的状态变化
    // 遍历S1-S4四个开关，检测是否有新的触发事件
    for( sw = SWITCH_S1; sw < SWITCH_COUNT; sw++)
    {
        // 检查开关状态是否发生变化且当前为触发状态（低电平）
        if(PhotoSwitch_IsChanged(sw) && PhotoSwitch_IsTriggered(sw))
        {
            if(!gesture_status.gesture_active)
            {
                // 【第一阶段】记录第一个开关触发
                // 这是手势识别的开始，记录起始开关和时间
                gesture_status.first_switch = sw;
                gesture_status.first_time = PhotoSwitch_GetTriggerTime(sw);
                gesture_status.gesture_active = 1;  // 激活手势识别状态机
            }
            else
            {
                // 【第二阶段】记录第二个开关触发并进行手势分析
                uint32_t interval;
                gesture_status.second_switch = sw;
                gesture_status.second_time = PhotoSwitch_GetTriggerTime(sw);

                // 计算两次触发的时间间隔
                interval = gesture_status.second_time - gesture_status.first_time;

                // 验证时间间隔是否在有效范围内
                // 太快（<50ms）可能是误触发，太慢（>800ms）可能是两个独立动作
                if(interval >= GESTURE_MIN_INTERVAL && interval <= GESTURE_MAX_INTERVAL)
                {
                    // 【手势分类】根据开关组合识别具体手势类型
                    detected_gesture = Gesture_ClassifyGesture(
                        gesture_status.first_switch,
                        gesture_status.second_switch
                    );

                    // 保存最后识别到的有效手势
                    if(detected_gesture != GESTURE_NONE)
                    {
                        gesture_status.last_gesture = detected_gesture;
                    }
                }
                else
                {
                    // 记录无效操作
                    slide_detection.invalid_count++;
                    slide_detection.last_invalid_time = current_time;
                    if(interval < GESTURE_MIN_INTERVAL)
                        slide_detection.last_result = SLIDE_RESULT_TOO_FAST;
                    else
                        slide_detection.last_result = SLIDE_RESULT_TOO_SLOW;
                }

                // 【状态重置】无论识别成功与否，都重置状态机准备下次识别
                Gesture_Reset();
                return detected_gesture;
            }
        }
    }
    
    return GESTURE_NONE;
}

/**
 * @brief  手势分类识别 - 根据开关组合确定手势类型
 * @param  first: 第一个触发的开关
 * @param  second: 第二个触发的开关
 * @retval 手势类型
 *
 * 【手势映射表】
 * 基本控制手势：
 * S1→S2: 风扇正转启动 (从左到右挥手)
 * S2→S1: 风扇正转停止 (从右到左挥手)
 * S3→S4: 风扇反转启动 (从上到下挥手)
 * S4→S3: 风扇反转停止 (从下到上挥手)
 *
 * 调节控制手势：
 * S1→S4: 电压上升 (对角线挥手，增加风扇电压)
 * S4→S1: 电压下降 (对角线挥手，减少风扇电压)
 *
 * 备用功能手势：
 * S4→S2: 备用功能1
 * S3→S1: 备用功能2
 * S3→S2: 时间设定 (需配合距离5-20cm)
 */
static GestureType_t Gesture_ClassifyGesture(PhotoSwitch_t first, PhotoSwitch_t second)
{
    // 根据开关组合判断手势类型
    if(first == SWITCH_S1 && second == SWITCH_S2)
        return GESTURE_S1_TO_S2;        // 风扇正转启动
    else if(first == SWITCH_S2 && second == SWITCH_S1)
        return GESTURE_S2_TO_S1;        // 风扇正转停止
    else if(first == SWITCH_S3 && second == SWITCH_S4)
        return GESTURE_S3_TO_S4;        // 风扇反转启动
    else if(first == SWITCH_S4 && second == SWITCH_S3)
        return GESTURE_S4_TO_S3;        // 风扇反转停止
    else if(first == SWITCH_S1 && second == SWITCH_S4)
        return GESTURE_S1_TO_S4;        // 电压上升
    else if(first == SWITCH_S4 && second == SWITCH_S1)
        return GESTURE_S4_TO_S1;        // 电压下降
    else if(first == SWITCH_S4 && second == SWITCH_S2)
        return GESTURE_S4_TO_S2;        // 备用
    else if(first == SWITCH_S3 && second == SWITCH_S1)
        return GESTURE_S3_TO_S1;        // 备用
    else if(first == SWITCH_S3 && second == SWITCH_S2)
        return GESTURE_S3_TO_S2;        // 时间设定
    else if(first == SWITCH_S4 && second == SWITCH_S1)
        return GESTURE_S4_TO_S1;        // 电压设定
    else
        return GESTURE_NONE;            // 无效手势组合
}

/**
 * @brief  重置手势状态
 * @param  None
 * @retval None
 */
void Gesture_Reset(void)
{
    gesture_status.first_switch = SWITCH_COUNT;
    gesture_status.second_switch = SWITCH_COUNT;
    gesture_status.first_time = 0;
    gesture_status.second_time = 0;
    gesture_status.gesture_active = 0;
}

/**
 * @brief  获取手势名称
 * @param  gesture: 手势类型
 * @retval 手势名称字符串
 */
const char* Gesture_GetName(GestureType_t gesture)
{
    if(gesture < GESTURE_COUNT)
        return gesture_names[gesture];
    else
        return "UNKNOWN";
}

/**
 * @brief  检查手势有效性
 * @param  gesture: 手势类型
 * @retval 1-有效, 0-无效
 */
uint8_t Gesture_IsValid(GestureType_t gesture)
{
    return (gesture > GESTURE_NONE && gesture < GESTURE_COUNT);
}

/**
 * @brief  滑动逻辑检测
 * @param  None
 * @retval 滑动检测结果
 *
 * 【检测逻辑】
 * 1. 检测两个连续光电门触发间隔是否>800ms或<50ms，判定为误操作
 * 2. 触发第一个光电门后1000ms内没有触发下一个光电门，判定为无效触发
 * 3. 记录无效操作次数和时间，用于统计分析
 */
SlideResult_t Gesture_CheckSlideLogic(void)
{
    uint32_t current_time = HAL_GetTick();
    SlideResult_t result = SLIDE_RESULT_NONE;

    // 检查是否有手势识别活动
    if(gesture_status.gesture_active)
    {
        uint32_t elapsed_time = current_time - gesture_status.first_time;

        // 检查超时（1000ms内没有第二个开关触发）
        if(elapsed_time > GESTURE_TIMEOUT_MS)
        {
            result = SLIDE_RESULT_TIMEOUT;
            slide_detection.invalid_count++;
            slide_detection.last_invalid_time = current_time;

            // 清除手势状态
            Gesture_Reset();
        }
    }

    // 检查最近完成的手势（如果有的话）
    if(gesture_status.second_time > 0 && gesture_status.first_time > 0)
    {
        uint32_t interval = gesture_status.second_time - gesture_status.first_time;

        if(interval < GESTURE_MIN_INTERVAL)
        {
            result = SLIDE_RESULT_TOO_FAST;
            slide_detection.invalid_count++;
            slide_detection.last_invalid_time = current_time;
        }
        else if(interval > GESTURE_MAX_INTERVAL)
        {
            result = SLIDE_RESULT_TOO_SLOW;
            slide_detection.invalid_count++;
            slide_detection.last_invalid_time = current_time;
        }
        else
        {
            result = SLIDE_RESULT_VALID;
        }
    }

    slide_detection.last_result = result;
    return result;
}

/**
 * @brief  清除无效操作标志
 * @param  None
 * @retval None
 */
void Gesture_ClearInvalidOperation(void)
{
    slide_detection.invalid_count = 0;
    slide_detection.last_invalid_time = 0;
    slide_detection.last_result = SLIDE_RESULT_NONE;

    // 重置手势状态
    Gesture_Reset();
}
