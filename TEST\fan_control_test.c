/**
 * @file    fan_control_test.c
 * @brief   风扇控制功能测试程序
 * <AUTHOR>
 * @date    2025-07-31
 */

#include "main.h"
#include "fan.h"
#include "gesture.h"
#include "display_update.h"

/**
 * @brief  测试风扇正转和反转功能
 * @param  None
 * @retval None
 */
void Test_FanDirectionControl(void)
{
    printf("=== 风扇方向控制测试 ===\n");
    
    // 测试正转启动
    printf("1. 测试正转启动...\n");
    Fan_StartForward();
    printf("   风扇状态: %s\n", Fan_IsRunning() ? "运行中" : "停止");
    printf("   转向: %s\n", Fan_GetDirection() == FAN_FORWARD ? "正转" : 
                         Fan_GetDirection() == FAN_REVERSE ? "反转" : "停止");
    delay_ms(2000);
    
    // 测试停止
    printf("2. 测试停止...\n");
    Fan_Stop();
    printf("   风扇状态: %s\n", Fan_IsRunning() ? "运行中" : "停止");
    delay_ms(1000);
    
    // 测试反转启动
    printf("3. 测试反转启动...\n");
    Fan_StartReverse();
    printf("   风扇状态: %s\n", Fan_IsRunning() ? "运行中" : "停止");
    printf("   转向: %s\n", Fan_GetDirection() == FAN_FORWARD ? "正转" : 
                         Fan_GetDirection() == FAN_REVERSE ? "反转" : "停止");
    delay_ms(2000);
    
    // 测试停止
    printf("4. 测试停止...\n");
    Fan_Stop();
    printf("   风扇状态: %s\n", Fan_IsRunning() ? "运行中" : "停止");
    
    printf("=== 测试完成 ===\n\n");
}

/**
 * @brief  测试手势识别功能
 * @param  None
 * @retval None
 */
void Test_GestureRecognition(void)
{
    printf("=== 手势识别测试 ===\n");
    
    // 测试各种手势类型
    GestureType_t test_gestures[] = {
        GESTURE_S1_TO_S2,   // 正转启动
        GESTURE_S2_TO_S1,   // 正转停止
        GESTURE_S3_TO_S4,   // 反转启动
        GESTURE_S4_TO_S3,   // 反转停止
        GESTURE_S1_TO_S4,   // 电压上升
        GESTURE_S4_TO_S1    // 电压下降
    };
    
    for(int i = 0; i < sizeof(test_gestures)/sizeof(test_gestures[0]); i++)
    {
        printf("%d. 手势: %s\n", i+1, Gesture_GetName(test_gestures[i]));
        printf("   有效性: %s\n", Gesture_IsValid(test_gestures[i]) ? "有效" : "无效");
    }
    
    printf("=== 测试完成 ===\n\n");
}

/**
 * @brief  模拟手势控制测试
 * @param  None
 * @retval None
 */
void Test_GestureControl(void)
{
    printf("=== 手势控制模拟测试 ===\n");
    
    // 模拟S1→S2手势（正转启动）
    printf("1. 模拟S1→S2手势（正转启动）\n");
    GestureType_t gesture = GESTURE_S1_TO_S2;
    
    if(!Fan_IsRunning())
    {
        Fan_StartForward();
        printf("   执行: 风扇正转启动\n");
    }
    printf("   状态: %s, 转向: %s\n", 
           Fan_IsRunning() ? "运行" : "停止",
           Fan_GetDirection() == FAN_FORWARD ? "正转" : 
           Fan_GetDirection() == FAN_REVERSE ? "反转" : "停止");
    delay_ms(2000);
    
    // 模拟S3→S4手势（反转启动）
    printf("2. 模拟S3→S4手势（反转启动）\n");
    gesture = GESTURE_S3_TO_S4;
    
    if(Fan_GetDirection() != FAN_REVERSE)
    {
        Fan_StartReverse();
        printf("   执行: 切换到反转\n");
    }
    printf("   状态: %s, 转向: %s\n", 
           Fan_IsRunning() ? "运行" : "停止",
           Fan_GetDirection() == FAN_FORWARD ? "正转" : 
           Fan_GetDirection() == FAN_REVERSE ? "反转" : "停止");
    delay_ms(2000);
    
    // 模拟S4→S3手势（反转停止）
    printf("3. 模拟S4→S3手势（反转停止）\n");
    gesture = GESTURE_S4_TO_S3;
    
    if(Fan_IsRunning() && Fan_GetDirection() == FAN_REVERSE)
    {
        Fan_Stop();
        printf("   执行: 停止反转风扇\n");
    }
    printf("   状态: %s\n", Fan_IsRunning() ? "运行" : "停止");
    
    printf("=== 测试完成 ===\n\n");
}

/**
 * @brief  主测试函数
 * @param  None
 * @retval None
 */
void RunFanControlTests(void)
{
    printf("\n========== 风扇控制系统测试 ==========\n");
    
    // 初始化测试环境
    Fan_Init();
    Gesture_Init();
    
    // 运行各项测试
    Test_FanDirectionControl();
    Test_GestureRecognition();
    Test_GestureControl();
    
    printf("========== 所有测试完成 ==========\n\n");
}
