#include "fan.h"
#include "dac.h"
#include "delay.h"
#include "usart.h"
#include <stdio.h>

//////////////////////////////////////////////////////////////////////////////////	 
// DAC控制风扇测试程序
// 测试IO口控制正反转 + DAC控制转速功能
//////////////////////////////////////////////////////////////////////////////////

/**
 * @brief  测试DAC风扇控制功能
 * @param  None
 * @retval None
 */
void Test_DAC_Fan_Control(void)
{
    printf("=== DAC风扇控制测试开始 ===\r\n");
    
    // 1. 测试风扇初始化
    printf("1. 初始化风扇模块...\r\n");
    Fan_Init();
    delay_ms(1000);
    
    // 2. 测试停止状态
    printf("2. 测试停止状态 (DAC=0)...\r\n");
    Fan_Stop();
    printf("   风扇状态: %s, DAC值: %d, 等效电压: %dmV\r\n", 
           Fan_IsRunning() ? "运行" : "停止", 
           Fan_GetSpeed(), 
           Fan_GetVoltage());
    delay_ms(2000);
    
    // 3. 测试正转启动
    printf("3. 测试正转启动 (默认速度)...\r\n");
    Fan_StartForward();
    delay_ms(1000);
    printf("   风扇状态: %s, 方向: %d, DAC值: %d, 等效电压: %dmV\r\n", 
           Fan_IsRunning() ? "运行" : "停止", 
           Fan_GetDirection(),
           Fan_GetSpeed(), 
           Fan_GetVoltage());
    delay_ms(3000);
    
    // 4. 测试速度调节 - 上升
    printf("4. 测试速度上升 (25%% -> 100%%)...\r\n");
    Fan_StartSpeedAdjust(VOLTAGE_ADJUST_UP);
    
    // 监控速度调节过程
    for(int i = 0; i < 50; i++)
    {
        Fan_Update();
        if(i % 10 == 0)
        {
            printf("   进度 %d/50: DAC值=%d, 等效电压=%dmV\r\n", 
                   i, Fan_GetSpeed(), Fan_GetVoltage());
        }
        delay_ms(100);
        
        if(!Fan_IsSpeedAdjusting()) break;
    }
    
    printf("   速度上升完成: DAC值=%d, 等效电压=%dmV\r\n", 
           Fan_GetSpeed(), Fan_GetVoltage());
    delay_ms(2000);
    
    // 5. 测试速度调节 - 下降
    printf("5. 测试速度下降 (100%% -> 0%%)...\r\n");
    Fan_StartSpeedAdjust(VOLTAGE_ADJUST_DOWN);
    
    // 监控速度调节过程
    for(int i = 0; i < 50; i++)
    {
        Fan_Update();
        if(i % 10 == 0)
        {
            printf("   进度 %d/50: DAC值=%d, 等效电压=%dmV\r\n", 
                   i, Fan_GetSpeed(), Fan_GetVoltage());
        }
        delay_ms(100);
        
        if(!Fan_IsSpeedAdjusting()) break;
    }
    
    printf("   速度下降完成: DAC值=%d, 等效电压=%dmV\r\n", 
           Fan_GetSpeed(), Fan_GetVoltage());
    delay_ms(2000);
    
    // 6. 测试反转
    printf("6. 测试反转启动...\r\n");
    Fan_StartReverse();
    delay_ms(1000);
    printf("   风扇状态: %s, 方向: %d, DAC值: %d, 等效电压: %dmV\r\n", 
           Fan_IsRunning() ? "运行" : "停止", 
           Fan_GetDirection(),
           Fan_GetSpeed(), 
           Fan_GetVoltage());
    delay_ms(3000);
    
    // 7. 测试手动设置不同速度
    printf("7. 测试手动设置不同速度...\r\n");
    
    uint16_t test_speeds[] = {512, 1024, 2048, 3072, 4095}; // 12.5%, 25%, 50%, 75%, 100%
    const char* speed_names[] = {"12.5%", "25%", "50%", "75%", "100%"};
    
    for(int i = 0; i < 5; i++)
    {
        printf("   设置速度为 %s (DAC=%d)...\r\n", speed_names[i], test_speeds[i]);
        Fan_SetSpeed(test_speeds[i]);
        delay_ms(1000);
        printf("   当前: DAC值=%d, 等效电压=%dmV\r\n", 
               Fan_GetSpeed(), Fan_GetVoltage());
        delay_ms(2000);
    }
    
    // 8. 测试停止
    printf("8. 测试停止风扇...\r\n");
    Fan_Stop();
    delay_ms(1000);
    printf("   风扇状态: %s, DAC值: %d, 等效电压: %dmV\r\n", 
           Fan_IsRunning() ? "运行" : "停止", 
           Fan_GetSpeed(), 
           Fan_GetVoltage());
    
    printf("=== DAC风扇控制测试完成 ===\r\n\r\n");
}

/**
 * @brief  测试方向控制IO
 * @param  None
 * @retval None
 */
void Test_Direction_Control(void)
{
    printf("=== 方向控制IO测试开始 ===\r\n");

    // 测试各种方向设置
    printf("1. 测试停止状态 (DIR=0)...\r\n");
    Fan_SetDirection(FAN_STOP);
    delay_ms(2000);

    printf("2. 测试正转状态 (DIR=1, 高电平)...\r\n");
    Fan_SetDirection(FAN_FORWARD);
    delay_ms(2000);

    printf("3. 测试反转状态 (DIR=0, 低电平)...\r\n");
    Fan_SetDirection(FAN_REVERSE);
    delay_ms(2000);

    printf("4. 回到停止状态 (DIR=0)...\r\n");
    Fan_SetDirection(FAN_STOP);
    delay_ms(1000);

    printf("=== 方向控制IO测试完成 ===\r\n\r\n");
}

/**
 * @brief  综合测试DAC风扇控制
 * @param  None
 * @retval None
 */
void Test_Comprehensive_DAC_Fan(void)
{
    printf("=== 综合DAC风扇控制测试 ===\r\n");
    
    // 初始化
    Fan_Init();
    
    // 测试方向控制
    Test_Direction_Control();
    
    // 测试完整功能
    Test_DAC_Fan_Control();
    
    printf("=== 所有测试完成 ===\r\n");
}
