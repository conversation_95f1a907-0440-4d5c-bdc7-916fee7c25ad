#include "../APP/DISPLAY_UPDATE/display_update.h"
#include "stdio.h"

//////////////////////////////////////////////////////////////////////////////////	 
// 非接触式控制盘 - 显示更新模块实现
//////////////////////////////////////////////////////////////////////////////////

// 全局变量定义
char display_str[DISPLAY_STR_SIZE];
uint32_t last_display_update = 0;
int rounded_distance = 0;

// 外部变量声明
extern FanControl_t fan_control;

/**
 * @brief  显示初始化 - 设置固定标签和标题
 * @param  None
 * @retval None
 */
void Display_Init(void)
{
    // 清屏
    LCD_Clear(WHITE);
    
    // 显示系统标题
    POINT_COLOR = BLUE;
    LCD_ShowString(30,10,300,24,24,"STM32F407 Control Panel");
    LCD_ShowString(30,40,300,16,16,"Non-Contact Fan Control");
    
    // 显示数据标签
    POINT_COLOR = RED;
    LCD_ShowString(30,70,300,16,16,"Distance (Exact):");
    LCD_ShowString(30,90,300,16,16,"Distance (Rounded):");
    LCD_ShowString(30,120,300,16,16,"System State:");
    LCD_ShowString(30,150,300,16,16,"Fan Status:");
    LCD_ShowString(30,180,300,16,16,"Fan Voltage:");
    LCD_ShowString(30,210,300,16,16,"Current Gesture:");
    
    // 显示状态标签
    POINT_COLOR = DARKBLUE;
    LCD_ShowString(30,270,300,16,16,"PhotoSwitches: S1   S2   S3   S4");
    LCD_ShowString(30,300,300,16,16,"Command Status:");
    LCD_ShowString(30,330,300,16,16,"DAC Output:");
    
    // 显示系统状态提示
    POINT_COLOR = GREEN;
    LCD_ShowString(30,360,300,16,24,"Everything is ok!");
}

/**
 * @brief  显示更新主函数 - 更新所有动态显示内容
 * @param  None
 * @retval None
 */
void Display_Update(void)
{
    // 这个函数将在main.c中调用时传入参数
    // 这里提供一个空的实现，具体的显示更新由各个子函数完成
}

/**
 * @brief  显示距离信息 - 精确值和四舍五入值
 * @param  distance: 测量距离
 * @retval None
 */
void Display_ShowDistance(float distance)
{
    // 显示精确距离值
    if(Ultrasonic_IsInRange(distance))
    {
        sprintf(display_str, "%.2f cm  ", distance);
        POINT_COLOR = BLACK;
    }
    else
    {
        sprintf(display_str, "Out of Range");
        POINT_COLOR = RED;
    }
    LCD_ShowString(180,70,120,16,16,(u8*)display_str);
    
    // 显示四舍五入后的距离值
    if(Ultrasonic_IsInRange(distance))
    {
        rounded_distance = (int)(distance + 0.5f);  // 四舍五入
        sprintf(display_str, "%d cm  ", rounded_distance);
        POINT_COLOR = BLUE;
    }
    else
    {
        sprintf(display_str, "---");
        POINT_COLOR = RED;
    }
    LCD_ShowString(180,90,120,16,16,(u8*)display_str);
}

/**
 * @brief  显示系统状态
 * @param  state: 系统状态
 * @retval None
 */
void Display_ShowSystemState(SystemState_t state)
{
    sprintf(display_str, "%s    ", StateMachine_GetStateName(state));
    POINT_COLOR = MAGENTA;
    LCD_ShowString(150,120,150,16,16,(u8*)display_str);
}

/**
 * @brief  显示风扇状态
 * @param  None
 * @retval None
 */
void Display_ShowFanStatus(void)
{
    // 显示风扇运行状态和转向
    if(Fan_IsRunning())
    {
        FanDirection_t direction = Fan_GetDirection();
        const char* direction_str = "";

        switch(direction)
        {
            case FAN_FORWARD:
                direction_str = "Forward";
                break;
            case FAN_REVERSE:
                direction_str = "Reverse";
                break;
            default:
                direction_str = "Unknown";
                break;
        }

        sprintf(display_str, "Running %s (DAC: %d)    ", direction_str, fan_control.speed_dac);
        POINT_COLOR = GREEN;
    }
    else
    {
        sprintf(display_str, "Stopped    ");
        POINT_COLOR = RED;
    }
    LCD_ShowString(150,150,200,16,16,(u8*)display_str);

    // 显示风扇电压
    sprintf(display_str, "%.1fV    ", Fan_GetVoltage() / 1000.0f);
    POINT_COLOR = BROWN;
    LCD_ShowString(150,180,100,16,16,(u8*)display_str);
}

/**
 * @brief  显示手势信息
 * @param  gesture: 当前手势
 * @retval None
 */
void Display_ShowGesture(GestureType_t gesture)
{
    if(gesture != GESTURE_NONE)
    {
        sprintf(display_str, "%s    ", Gesture_GetName(gesture));
        POINT_COLOR = MAGENTA;
        LCD_ShowString(180,210,120,16,16,(u8*)display_str);
    }
    else
    {
        sprintf(display_str, "None    ");
        POINT_COLOR = GRAY;
        LCD_ShowString(180,210,120,16,16,(u8*)display_str);
    }
}

/**
 * @brief  显示光电门状态
 * @param  None
 * @retval None
 */
void Display_ShowPhotoSwitches(void)
{
    // S1状态 (PC0)
    GPIO_PinState s1_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_0);
	// S2状态 (PC1)
    GPIO_PinState s2_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_1);
	// S3状态 (PC2)
    GPIO_PinState s3_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_2);
	// S4状态 (PC3)
    GPIO_PinState s4_state = HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_3);
    if(s1_state == GPIO_PIN_RESET)  // 低电平表示触发
    {
        POINT_COLOR = GREEN;
        LCD_ShowString(170,270,30,16,16,"ON ");
    }
    else
    {
        POINT_COLOR = RED;
        LCD_ShowString(170,270,30,16,16,"OFF");
    }
    

    if(s2_state == GPIO_PIN_RESET)
    {
        POINT_COLOR = GREEN;
        LCD_ShowString(210,270,30,16,16,"ON ");
    }
    else
    {
        POINT_COLOR = RED;
        LCD_ShowString(210,270,30,16,16,"OFF");
    }
    

    if(s3_state == GPIO_PIN_RESET)
    {
        POINT_COLOR = GREEN;
        LCD_ShowString(250,270,30,16,16,"ON ");
    }
    else
    {
        POINT_COLOR = RED;
        LCD_ShowString(250,270,30,16,16,"OFF");
    }
    

    if(s4_state == GPIO_PIN_RESET)
    {
        POINT_COLOR = GREEN;
        LCD_ShowString(290,270,30,16,16,"ON ");
    }
    else
    {
        POINT_COLOR = RED;
        LCD_ShowString(290,270,30,16,16,"OFF");
    }
}

/**
 * @brief  显示命令状态
 * @param  gesture: 当前手势
 * @retval None
 */
void Display_ShowCommand(GestureType_t gesture)
{
    if(gesture != GESTURE_NONE)
    {
        // 根据手势显示对应的命令
        const char* command_str = "";
        switch(gesture)
        {
            case GESTURE_S1_TO_S2:
                command_str = "CMD: Fan Start";
                break;
            case GESTURE_S2_TO_S1:
                command_str = "CMD: Fan Stop";
                break;
            case GESTURE_S3_TO_S4:
                command_str = "CMD: Fan Start";
                break;
            case GESTURE_S4_TO_S3:
                command_str = "CMD: Fan Stop";
                break;
            case GESTURE_S4_TO_S2:
                command_str = "CMD: PWM Speed Up";
                break;
            case GESTURE_S3_TO_S1:
                command_str = "CMD: PWM Speed Down";
                break;
            case GESTURE_S3_TO_S2:
                command_str = "CMD: Time Setting";
                break;
            case GESTURE_S4_TO_S1:
                command_str = "CMD: Voltage Setting";
                break;
            default:
                command_str = "CMD: Unknown";
                break;
        }
        sprintf(display_str, "%s    ", command_str);
        POINT_COLOR = BLUE;
    }
    else
    {
        sprintf(display_str, "CMD: Waiting for gesture...    ");
        POINT_COLOR = GRAY;
    }
    LCD_ShowString(150,300,300,16,16,(u8*)display_str);
}

/**
 * @brief  显示DAC输出信息
 * @param  dac_voltage: DAC输出电压
 * @param  distance: 当前距离
 * @retval None
 */
void Display_ShowDAC(float dac_voltage, float distance)
{
    sprintf(display_str, "%.2fV (Distance: %.1fcm)    ", dac_voltage, distance);
    POINT_COLOR = BRED;
    LCD_ShowString(150,330,300,16,16,(u8*)display_str);
}
