#include "info.h"
#include "stdio.h"
#include "string.h"
#include "usart.h"

//////////////////////////////////////////////////////////////////////////////////
// 非接触式控制盘 - 串口屏信息发送模块实现
//////////////////////////////////////////////////////////////////////////////////

// 全局变量定义
char info_buffer[INFO_BUFFER_SIZE];

/**
 * @brief  信息模块初始化
 * @param  None
 * @retval None
 */
void Info_Init(void)
{
    // 清空缓冲区
    memset(info_buffer, 0, INFO_BUFFER_SIZE);

    // 发送初始化信息到串口屏
    Info_SendSetTime(0);
    Info_SendCountdown(0);
    Info_SendSetVoltage(0.0f);
    Info_SendDistance(0.0f);
    Info_SendRPM(0);
    Info_SendDirection(FAN_DIR_STOP);
    Info_SendMotionState(MOTION_STOP);
}

/**
 * @brief  发送命令到串口屏
 * @param  command: 要发送的命令字符串
 * @retval None
 */
void Info_SendCommand(const char* command)
{
    uint8_t end_cmd[] = INFO_END_CMD;

    // 发送命令字符串
    HAL_UART_Transmit(&UART1_Handler, (uint8_t*)command, strlen(command), 1000);

    // 发送结束符
    HAL_UART_Transmit(&UART1_Handler, end_cmd, 3, 1000);
}

/**
 * @brief  发送设定运行时间
 * @param  time_seconds: 设定时间(秒)
 * @retval None
 */
void Info_SendSetTime(uint16_t time_seconds)
{
    sprintf(info_buffer, "t0_set_time.txt=\"%ds\"", time_seconds);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  发送倒计时
 * @param  countdown_seconds: 倒计时(秒)
 * @retval None
 */
void Info_SendCountdown(uint16_t countdown_seconds)
{
    sprintf(info_buffer, "t1_countdown.txt=\"%ds\"", countdown_seconds);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  发送设定工作电压
 * @param  voltage: 电压值(V)
 * @retval None
 */
void Info_SendSetVoltage(float voltage)
{
    sprintf(info_buffer, "t2_set_u.txt=\"%.1fV\"", voltage);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  发送操作距离
 * @param  distance: 距离值(cm)
 * @retval None
 */
void Info_SendDistance(float distance)
{
    sprintf(info_buffer, "t3_cmd_d.txt=\"%.1fcm\"", distance);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  发送风扇转速
 * @param  rpm: 转速(RPM)
 * @retval None
 */
void Info_SendRPM(uint16_t rpm)
{
    sprintf(info_buffer, "t4_rpm.txt=\"%dRPM\"", rpm);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  发送风扇转向
 * @param  direction: 风扇转向
 * @retval None
 */
void Info_SendDirection(InfoFanDirection_t direction)
{
    const char* dir_str = "";

    switch(direction)
    {
        case FAN_DIR_FORWARD:
            dir_str = " 正转 ";
            break;
        case FAN_DIR_REVERSE:
            dir_str = " 反转 ";
            break;
        case FAN_DIR_STOP:
        default:
            dir_str = " 停止 ";
            break;
    }

    sprintf(info_buffer, "t5_direction.txt=\" %s \"", dir_str);
    Info_SendCommand(info_buffer);
}

/**
 * @brief  发送运动状态
 * @param  state: 运动状态
 * @retval None
 */
void Info_SendMotionState(InfoMotionState_t state)
{
    if(state == MOTION_RUNNING)
    {
        sprintf(info_buffer, "vis 9,1");  // 显示运行图片
    }
    else
    {
        sprintf(info_buffer, "vis 9,0");  // 显示停止图片
    }

    Info_SendCommand(info_buffer);
}

/**
 * @brief  更新最终测定的距离给串口屏
 * @param  None
 * @retval None
 */
void Info_SendFinalDistance(int FinalDistance){

    sprintf(info_buffer, "t6_final_d.txt=\"最终距离为:%d\"", FinalDistance);
    Info_SendCommand(info_buffer);
}


/**
 * @brief  更新所有信息 - 批量发送当前状态
 * @param  None
 * @retval None
 */
void Info_UpdateAll(void)
{
    // 这个函数可以在需要时批量更新所有信息
    // 具体的数值需要从其他模块获取
    // 这里提供一个框架，实际使用时传入具体参数
}



/**
 * @brief  测试串口屏通信
 * @param  None
 * @retval None
 */
void Info_Test(void)
{
    // 发送测试信息
    Info_SendSetTime(30);
    Info_SendCountdown(25);
    Info_SendSetVoltage(5.5f);
    Info_SendDistance(12.3f);
    Info_SendRPM(1500);
    Info_SendDirection(FAN_DIR_FORWARD);
    Info_SendMotionState(MOTION_RUNNING);
}

/**
 * @brief  根据系统状态发送时间相关信息
 * @param  set_time: 设定时间(秒)
 * @param  countdown: 倒计时(秒)
 * @retval None
 */
void Info_SendTimeInfo(uint16_t set_time, uint16_t countdown)
{
    Info_SendSetTime(set_time);
    Info_SendCountdown(countdown);
}

/**
 * @brief  根据风扇状态发送完整风扇信息
 * @param  is_running: 风扇是否运行
 * @param  voltage: 风扇电压(V)
 * @param  pwm_duty: PWM占空比(0-1000)
 * @retval None
 */
void Info_SendFanInfo(uint8_t is_running, float voltage, uint16_t pwm_duty)
{
	uint16_t estimated_rpm = 0;
    // 发送电压
    Info_SendSetVoltage(voltage);

    // 发送转速(根据PWM占空比估算)
    estimated_rpm = is_running ? (pwm_duty * 30) : 0;  // 简单估算公式
    Info_SendRPM(estimated_rpm);

    // 发送转向和运动状态
    if(is_running) {
        Info_SendDirection(FAN_DIR_FORWARD);  // 简化为正转
        Info_SendMotionState(MOTION_RUNNING);
    } else {
        Info_SendDirection(FAN_DIR_STOP);
        Info_SendMotionState(MOTION_STOP);
    }
}
