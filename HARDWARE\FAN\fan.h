#ifndef __FAN_H
#define __FAN_H
#include "sys.h"

//////////////////////////////////////////////////////////////////////////////////	 
// 非接触式控制盘 - 风扇控制模块
// 控制12V直流风扇的启停、正反转、调速
// 使用PWM + H桥驱动电路
//////////////////////////////////////////////////////////////////////////////////

// 风扇控制引脚定义 - IO口控制正反转 + DAC控制转速
#define FAN_DIR_GPIO_PORT   GPIOA
#define FAN_DIR_GPIO_PIN    GPIO_PIN_6      // 方向控制 (高电平=正转, 低电平=反转)
#define FAN_EN_GPIO_PORT    GPIOA
#define FAN_EN_GPIO_PIN     GPIO_PIN_5      // 风扇使能控制（可选）

// 风扇转向枚举
typedef enum {
    FAN_STOP = 0,       // 停止
    FAN_FORWARD,        // 正转
    FAN_REVERSE         // 反转
} FanDirection_t;

// 电压调节状态枚举
typedef enum {
    VOLTAGE_ADJUST_NONE = 0,    // 无调节
    VOLTAGE_ADJUST_UP,          // 电压上升中
    VOLTAGE_ADJUST_DOWN         // 电压下降中
} VoltageAdjustState_t;

// 风扇参数结构体
typedef struct {
    FanDirection_t direction;   // 转向
    uint16_t speed_dac;         // DAC转速控制值 0-4095 (0=停止)
    uint16_t voltage_mv;        // 等效电压(mV) 0-3300 (对应DAC值)
    uint8_t is_running;         // 运行状态
    uint32_t run_time_ms;       // 运行时间(ms)
    uint32_t start_time;        // 启动时间戳

    // 速度调节相关
    VoltageAdjustState_t speed_adjust_state;    // 速度调节状态
    uint16_t target_speed_dac;                  // 目标DAC值
    uint16_t start_speed_dac;                   // 起始DAC值
    uint32_t adjust_start_time;                 // 调节开始时间
    uint32_t adjust_duration_ms;                // 调节持续时间(ms)
} FanControl_t;

// DAC转速控制参数定义
#define FAN_DAC_MIN         0       // 最小DAC值 (停止)
#define FAN_DAC_MAX         4095    // 最大DAC值 (最高速)
#define FAN_DAC_DEFAULT     1024    // 默认DAC值 (25%速度)
#define FAN_VOLTAGE_REF     3300    // DAC参考电压3.3V (mV)

// 速度调节参数定义
#define SPEED_ADJUST_DURATION_MS  5000    // 速度调节持续时间5秒

// 全局变量声明
extern FanControl_t fan_control;
extern DAC_HandleTypeDef hdac;

// 函数声明
void Fan_Init(void);                            // 风扇模块初始化
void Fan_Start(void);                           // 启动风扇（保持当前方向）
void Fan_StartForward(void);                    // 启动风扇正转
void Fan_StartReverse(void);                    // 启动风扇反转
void Fan_Stop(void);                            // 停止风扇
void Fan_SetSpeed(uint16_t dac_value);          // 设置转速(DAC值 0-4095)
void Fan_SetDirection(FanDirection_t direction); // 设置转向
uint16_t Fan_GetSpeed(void);                    // 获取当前转速DAC值
uint16_t Fan_GetVoltage(void);                  // 获取当前等效电压
FanDirection_t Fan_GetDirection(void);          // 获取当前转向
uint8_t Fan_IsRunning(void);                    // 检查运行状态
void Fan_Update(void);                          // 更新风扇状态

// 速度调节函数
void Fan_StartSpeedAdjust(VoltageAdjustState_t adjust_type); // 开始速度调节
void Fan_StopSpeedAdjust(void);                 // 停止速度调节
uint8_t Fan_IsSpeedAdjusting(void);             // 检查是否正在调节速度
void Fan_UpdateSpeedAdjust(void);               // 更新速度调节

#endif
