#include "fan.h"

//////////////////////////////////////////////////////////////////////////////////	 
// 非接触式控制盘 - 风扇控制模块实现
//////////////////////////////////////////////////////////////////////////////////

// 全局变量定义
FanControl_t fan_control;
extern DAC_HandleTypeDef hdac;

// 风扇电压-DAC查找表：0V到12V，每0.5V一个点
// 索引0=0V, 索引1=0.5V, 索引2=1.0V, ..., 索引24=12V
const uint16_t fan_voltage_dac_table[FAN_VOLTAGE_TABLE_SIZE] = {
    0,      // 0.0V  -> DAC 0
    171,    // 0.5V  -> DAC 171   (0.5/12*4095)
    341,    // 1.0V  -> DAC 341   (1.0/12*4095)
    512,    // 1.5V  -> DAC 512   (1.5/12*4095)
    683,    // 2.0V  -> DAC 683   (2.0/12*4095)
    853,    // 2.5V  -> DAC 853   (2.5/12*4095)
    1024,   // 3.0V  -> DAC 1024  (3.0/12*4095)
    1195,   // 3.5V  -> DAC 1195  (3.5/12*4095)
    1365,   // 4.0V  -> DAC 1365  (4.0/12*4095)
    1536,   // 4.5V  -> DAC 1536  (4.5/12*4095)
    1707,   // 5.0V  -> DAC 1707  (5.0/12*4095)
    1877,   // 5.5V  -> DAC 1877  (5.5/12*4095)
    2048,   // 6.0V  -> DAC 2048  (6.0/12*4095)
    2219,   // 6.5V  -> DAC 2219  (6.5/12*4095)
    2389,   // 7.0V  -> DAC 2389  (7.0/12*4095)
    2560,   // 7.5V  -> DAC 2560  (7.5/12*4095)
    2731,   // 8.0V  -> DAC 2731  (8.0/12*4095)
    2901,   // 8.5V  -> DAC 2901  (8.5/12*4095)
    3072,   // 9.0V  -> DAC 3072  (9.0/12*4095)
    3243,   // 9.5V  -> DAC 3243  (9.5/12*4095)
    3413,   // 10.0V -> DAC 3413  (10.0/12*4095)
    3584,   // 10.5V -> DAC 3584  (10.5/12*4095)
    3755,   // 11.0V -> DAC 3755  (11.0/12*4095)
    3925,   // 11.5V -> DAC 3925  (11.5/12*4095)
    4095    // 12.0V -> DAC 4095  (12.0/12*4095)
};

/**
 * @brief  风扇模块初始化
 * @param  None
 * @retval None
 */
void Fan_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    DAC_ChannelConfTypeDef sConfig = {0};

    // 使能时钟
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_DAC_CLK_ENABLE();

    // 配置方向控制引脚 (PA6 - DIR)
    GPIO_InitStruct.Pin = FAN_DIR_GPIO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(FAN_DIR_GPIO_PORT, &GPIO_InitStruct);

    // 配置使能引脚 (PA5 - 可选)
    GPIO_InitStruct.Pin = FAN_EN_GPIO_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(FAN_EN_GPIO_PORT, &GPIO_InitStruct);

    // 配置DAC (PA4 - DAC_OUT1)
    hdac.Instance = DAC;
    if (HAL_DAC_Init(&hdac) != HAL_OK)
    {
        while(1); // 初始化错误
    }

    // 配置DAC通道1
    sConfig.DAC_Trigger = DAC_TRIGGER_NONE;
    sConfig.DAC_OutputBuffer = DAC_OUTPUTBUFFER_ENABLE;

    if (HAL_DAC_ConfigChannel(&hdac, &sConfig, DAC_CHANNEL_1) != HAL_OK)
    {
        while(1); // 配置错误
    }

    // 启动DAC
    HAL_DAC_Start(&hdac, DAC_CHANNEL_1);
    
    // 初始化风扇控制结构体
    fan_control.direction = FAN_STOP;
    fan_control.speed_dac = FAN_DAC_MIN;
    fan_control.voltage_mv = 0;
    fan_control.is_running = 0;
    fan_control.run_time_ms = 0;
    fan_control.start_time = 0;

    // 初始化速度调节相关参数
    fan_control.speed_adjust_state = VOLTAGE_ADJUST_NONE;
    fan_control.target_speed_dac = FAN_DAC_MIN;
    fan_control.start_speed_dac = FAN_DAC_MIN;
    fan_control.target_voltage_level = 0;
    fan_control.start_voltage_level = 0;
    fan_control.adjust_start_time = 0;
    fan_control.adjust_duration_ms = SPEED_ADJUST_DURATION_MS;
    
    // 初始状态：停止
    Fan_Stop();
}

/**
 * @brief  启动风扇
 * @param  None
 * @retval None
 */
void Fan_Start(void)
{
    // 如果当前是停止状态，默认启动正转
    if(fan_control.direction == FAN_STOP)
    {
        fan_control.direction = FAN_FORWARD;
    }
    // 否则保持当前方向

    fan_control.is_running = 1;
    fan_control.start_time = HAL_GetTick();

    // 设置方向控制IO
    Fan_SetDirection(fan_control.direction);

    // 设置默认转速
    if(fan_control.speed_dac == 0)
    {
        Fan_SetSpeed(FAN_DAC_DEFAULT);
    }
    else
    {
        Fan_SetSpeed(fan_control.speed_dac);
    }
}

/**
 * @brief  启动风扇正转
 * @param  None
 * @retval None
 */
void Fan_StartForward(void)
{
    fan_control.direction = FAN_FORWARD;
    fan_control.is_running = 1;
    fan_control.start_time = HAL_GetTick();

    // 设置正转方向
    Fan_SetDirection(FAN_FORWARD);

    // 设置默认转速
    if(fan_control.speed_dac == 0)
    {
        Fan_SetSpeed(FAN_DAC_DEFAULT);
    }
    else
    {
        Fan_SetSpeed(fan_control.speed_dac);
    }
}

/**
 * @brief  启动风扇反转
 * @param  None
 * @retval None
 */
void Fan_StartReverse(void)
{
    fan_control.direction = FAN_REVERSE;
    fan_control.is_running = 1;
    fan_control.start_time = HAL_GetTick();

    // 设置反转方向
    Fan_SetDirection(FAN_REVERSE);

    // 设置默认转速
    if(fan_control.speed_dac == 0)
    {
        Fan_SetSpeed(FAN_DAC_DEFAULT);
    }
    else
    {
        Fan_SetSpeed(fan_control.speed_dac);
    }
}

/**
 * @brief  停止风扇
 * @param  None
 * @retval None
 */
void Fan_Stop(void)
{
    fan_control.direction = FAN_STOP;
    fan_control.is_running = 0;
    fan_control.speed_dac = 0;

    // 设置DAC为0，停止转动
    Fan_SetSpeed(0);

    // 关闭方向控制IO
    Fan_SetDirection(FAN_STOP);
}

/**
 * @brief  设置风扇转速
 * @param  dac_value: DAC值 (0-4095, 0=停止)
 * @retval None
 */
void Fan_SetSpeed(uint16_t dac_value)
{
    if(dac_value > FAN_DAC_MAX) dac_value = FAN_DAC_MAX;

    fan_control.speed_dac = dac_value;

    // 计算实际的风扇控制电压（考虑电压放大电路：DAC 0-3.3V 对应风扇控制电压 0-12V）
    fan_control.voltage_mv = (uint16_t)(dac_value * FAN_WORKING_VOLTAGE_MV / FAN_DAC_MAX);

    // 设置DAC输出
    HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, dac_value);
}

/**
 * @brief  通过电压值设置风扇转速（使用查找表）
 * @param  voltage_mv: 目标电压值 (0-12000mV, 0=停止)
 * @retval None
 */
void Fan_SetSpeedByVoltage(uint16_t voltage_mv)
{
    uint16_t dac_value;
    uint16_t table_index;
	uint16_t voltage_remainder;
	
    // 限制电压范围
    if(voltage_mv > FAN_WORKING_VOLTAGE_MV) voltage_mv = FAN_WORKING_VOLTAGE_MV;

    // 计算查找表索引 (每0.5V一个点)
    table_index = voltage_mv / FAN_VOLTAGE_STEP_MV;

    // 防止索引越界
    if(table_index >= FAN_VOLTAGE_TABLE_SIZE)
        table_index = FAN_VOLTAGE_TABLE_SIZE - 1;

    // 从查找表获取DAC值
    dac_value = fan_voltage_dac_table[table_index];

    // 如果电压不是精确的0.5V倍数，进行线性插值
    voltage_remainder = voltage_mv % FAN_VOLTAGE_STEP_MV;
    if(voltage_remainder != 0 && table_index < (FAN_VOLTAGE_TABLE_SIZE - 1))
    {
        uint16_t next_dac = fan_voltage_dac_table[table_index + 1];
        uint16_t dac_diff = next_dac - dac_value;
        dac_value += (uint16_t)((dac_diff * voltage_remainder) / FAN_VOLTAGE_STEP_MV);
    }

    // 设置DAC值
    Fan_SetSpeed(dac_value);
}

/**
 * @brief  通过电压级别设置风扇转速（使用查找表）
 * @param  level: 电压级别 (0-24, 对应0V-12V, 每级0.5V)
 * @retval None
 */
void Fan_SetSpeedByLevel(uint8_t level)
{
    uint16_t dac_value;

    // 限制级别范围
    if(level >= FAN_VOLTAGE_TABLE_SIZE)
        level = FAN_VOLTAGE_TABLE_SIZE - 1;

    // 从查找表获取DAC值
    dac_value = fan_voltage_dac_table[level];

    // 设置DAC值
    Fan_SetSpeed(dac_value);
}

/**
 * @brief  设置风扇转向
 * @param  direction: 转向 (FAN_STOP/FAN_FORWARD/FAN_REVERSE)
 * @retval None
 */
void Fan_SetDirection(FanDirection_t direction)
{
    fan_control.direction = direction;

    switch(direction)
    {
        case FAN_FORWARD:
            // 正转：DIR=高电平
            HAL_GPIO_WritePin(FAN_DIR_GPIO_PORT, FAN_DIR_GPIO_PIN, GPIO_PIN_SET);
            break;

        case FAN_REVERSE:
            // 反转：DIR=低电平
            HAL_GPIO_WritePin(FAN_DIR_GPIO_PORT, FAN_DIR_GPIO_PIN, GPIO_PIN_RESET);
            break;

        case FAN_STOP:
        default:
            // 停止：DIR=低电平（默认反转方向，但DAC=0所以不转）
            HAL_GPIO_WritePin(FAN_DIR_GPIO_PORT, FAN_DIR_GPIO_PIN, GPIO_PIN_RESET);
            break;
    }
}

/**
 * @brief  获取当前转速DAC值
 * @param  None
 * @retval DAC值 (0-4095)
 */
uint16_t Fan_GetSpeed(void)
{
    return fan_control.speed_dac;
}

/**
 * @brief  获取当前等效电压
 * @param  None
 * @retval 电压值(mV)
 */
uint16_t Fan_GetVoltage(void)
{
    return fan_control.voltage_mv;
}

/**
 * @brief  获取当前电压级别
 * @param  None
 * @retval 电压级别(0-24, 对应0V-12V)
 */
uint8_t Fan_GetVoltageLevel(void)
{
    uint16_t current_voltage = fan_control.voltage_mv;
    uint8_t level = current_voltage / FAN_VOLTAGE_STEP_MV;

    // 防止越界
    if(level >= FAN_VOLTAGE_TABLE_SIZE)
        level = FAN_VOLTAGE_TABLE_SIZE - 1;

    return level;
}

/**
 * @brief  获取当前转向
 * @param  None
 * @retval 转向
 */
FanDirection_t Fan_GetDirection(void)
{
    return fan_control.direction;
}

/**
 * @brief  检查运行状态
 * @param  None
 * @retval 1-运行, 0-停止
 */
uint8_t Fan_IsRunning(void)
{
    return fan_control.is_running;
}

/**
 * @brief  更新风扇状态
 * @param  None
 * @retval None
 */
void Fan_Update(void)
{
    if(fan_control.is_running)
    {
        fan_control.run_time_ms = HAL_GetTick() - fan_control.start_time;
    }

    // 更新速度调节
    Fan_UpdateSpeedAdjust();
}

/**
 * @brief  开始速度调节
 * @param  adjust_type: 调节类型（上升/下降）
 * @retval None
 */
void Fan_StartSpeedAdjust(VoltageAdjustState_t adjust_type)
{
    // 只有在风扇运行时才能调节速度
    if(!fan_control.is_running) return;

    // 保存当前速度作为起始速度
    fan_control.start_speed_dac = fan_control.speed_dac;
    fan_control.adjust_start_time = HAL_GetTick();
    fan_control.speed_adjust_state = adjust_type;

    // 根据调节类型设置目标速度
    if(adjust_type == VOLTAGE_ADJUST_UP)
    {
        // 速度上升：从当前值到最大值
        fan_control.target_speed_dac = FAN_DAC_MAX;
    }
    else if(adjust_type == VOLTAGE_ADJUST_DOWN)
    {
        // 速度下降：从当前值到最小值
        fan_control.target_speed_dac = FAN_DAC_MIN;
    }
}

/**
 * @brief  开始电压调节（使用查找表方式）
 * @param  adjust_type: 调节类型（上升/下降）
 * @retval None
 */
void Fan_StartVoltageAdjust(VoltageAdjustState_t adjust_type)
{
    // 只有在风扇运行时才能调节速度
    if(!fan_control.is_running) return;

    // 获取当前电压级别作为起始级别
    fan_control.start_voltage_level = Fan_GetVoltageLevel();
    fan_control.adjust_start_time = HAL_GetTick();
    fan_control.speed_adjust_state = adjust_type;

    // 根据调节类型设置目标电压级别
    if(adjust_type == VOLTAGE_ADJUST_UP)
    {
        // 电压上升：从当前级别到最大级别(24级=12V)
        fan_control.target_voltage_level = FAN_VOLTAGE_TABLE_SIZE - 1;
    }
    else if(adjust_type == VOLTAGE_ADJUST_DOWN)
    {
        // 电压下降：从当前级别到最小级别(0级=0V)
        fan_control.target_voltage_level = 0;
    }
}

/**
 * @brief  停止速度调节
 * @param  None
 * @retval None
 */
void Fan_StopSpeedAdjust(void)
{
    fan_control.speed_adjust_state = VOLTAGE_ADJUST_NONE;
}

/**
 * @brief  检查是否正在调节速度
 * @param  None
 * @retval 1-正在调节, 0-未调节
 */
uint8_t Fan_IsSpeedAdjusting(void)
{
    return (fan_control.speed_adjust_state != VOLTAGE_ADJUST_NONE);
}

/**
 * @brief  更新速度调节
 * @param  None
 * @retval None
 */
void Fan_UpdateSpeedAdjust(void)
{
    uint32_t current_time;
    uint32_t elapsed_time;
    float progress;
    uint8_t new_level;
    int16_t level_diff;

    // 如果没有正在进行的速度调节，直接返回
    if(fan_control.speed_adjust_state == VOLTAGE_ADJUST_NONE) return;

    current_time = HAL_GetTick();
    elapsed_time = current_time - fan_control.adjust_start_time;

    // 计算调节进度（0.0-1.0）
    progress = (float)elapsed_time / fan_control.adjust_duration_ms;

    // 如果已经完成调节
    if(progress >= 1.0f)
    {
        // 设置为目标电压级别并停止调节
        Fan_SetSpeedByLevel(fan_control.target_voltage_level);
        Fan_StopSpeedAdjust();
        return;
    }

    // 计算当前应该设置的电压级别
    level_diff = (int16_t)fan_control.target_voltage_level - (int16_t)fan_control.start_voltage_level;
    new_level = fan_control.start_voltage_level + (uint8_t)(progress * level_diff);

    // 确保级别在有效范围内
    if(new_level >= FAN_VOLTAGE_TABLE_SIZE)
        new_level = FAN_VOLTAGE_TABLE_SIZE - 1;

    // 设置新的电压级别
    Fan_SetSpeedByLevel(new_level);
}
