#include "../APP/STATE_MACHINE/state_machine.h"
#include "../HARDWARE/ULTRASONIC/ultrasonic.h"

//////////////////////////////////////////////////////////////////////////////////	 
// 非接触式控制盘 - 状态机模块实现
//////////////////////////////////////////////////////////////////////////////////

// 全局变量定义
StateMachine_t state_machine;

// 静态函数声明
static SystemState_t StateMachine_ProcessIdle(GestureType_t gesture, float distance);
static SystemState_t StateMachine_ProcessFanControl(GestureType_t gesture);
static SystemState_t StateMachine_ProcessTimeSetting(GestureType_t gesture, float distance);
static SystemState_t StateMachine_ProcessVoltageSetting(GestureType_t gesture, float distance);
static SystemState_t StateMachine_ProcessComboSetting(GestureType_t gesture);
static SystemState_t StateMachine_ProcessComboRunning(void);

// 状态名称字符串
static const char* state_names[STATE_COUNT] = {
    "IDLE",
    "FAN_CONTROL",
    "TIME_SETTING",
    "VOLTAGE_SETTING", 
    "COMBO_SETTING",
    "COMBO_RUNNING"
};

/**
 * @brief  状态机初始化
 * @param  None
 * @retval None
 */
void StateMachine_Init(void)
{
    state_machine.current_state = STATE_IDLE;
    state_machine.last_state = STATE_IDLE;
    state_machine.fan_state = FAN_STATE_STOPPED;
    state_machine.state_enter_time = HAL_GetTick();
    state_machine.state_timeout = 0;
    state_machine.state_changed = 0;
}

/**
 * @brief  状态机处理函数
 * @param  gesture: 检测到的手势
 * @retval None
 */
void StateMachine_Process(GestureType_t gesture)
{
    SystemState_t new_state = state_machine.current_state;
    float distance = Ultrasonic_GetDistance();
    
    switch(state_machine.current_state)
    {
        case STATE_IDLE:
            new_state = StateMachine_ProcessIdle(gesture, distance);
            break;
            
        case STATE_FAN_CONTROL:
            new_state = StateMachine_ProcessFanControl(gesture);
            break;
            
        case STATE_TIME_SETTING:
            new_state = StateMachine_ProcessTimeSetting(gesture, distance);
            break;
            
        case STATE_VOLTAGE_SETTING:
            new_state = StateMachine_ProcessVoltageSetting(gesture, distance);
            break;
            
        case STATE_COMBO_SETTING:
            new_state = StateMachine_ProcessComboSetting(gesture);
            break;
            
        case STATE_COMBO_RUNNING:
            new_state = StateMachine_ProcessComboRunning();
            break;
            
        default:
            new_state = STATE_IDLE;
            break;
    }
    
    // 状态切换
    if(new_state != state_machine.current_state)
    {
        StateMachine_SetState(new_state);
    }
}

/**
 * @brief  处理待机状态
 * @param  gesture: 手势
 * @param  distance: 距离
 * @retval 新状态
 */
static SystemState_t StateMachine_ProcessIdle(GestureType_t gesture, float distance)
{
    switch(gesture)
    {
        case GESTURE_S1_TO_S2:
        case GESTURE_S3_TO_S4:
            return STATE_FAN_CONTROL;
            
        case GESTURE_S3_TO_S2:
            if(distance >= 5.0f && distance <= 20.0f)
                return STATE_TIME_SETTING;
            break;
            
        case GESTURE_S4_TO_S1:
            if(distance >= 5.0f && distance <= 20.0f)
                return STATE_VOLTAGE_SETTING;
            break;
            
        default:
            break;
    }
    
    return STATE_IDLE;
}

/**
 * @brief  处理风扇控制状态
 * @param  gesture: 手势
 * @retval 新状态
 */
static SystemState_t StateMachine_ProcessFanControl(GestureType_t gesture)
{
    switch(gesture)
    {
        case GESTURE_S1_TO_S2:
        case GESTURE_S3_TO_S4:
            state_machine.fan_state = FAN_STATE_FORWARD;  // 简化为运行状态
            Fan_Start();  // 启动风扇
            break;
            
        case GESTURE_S2_TO_S1:
        case GESTURE_S4_TO_S3:
            state_machine.fan_state = FAN_STATE_STOPPED;
            Fan_Stop();
            return STATE_IDLE;
            
        case GESTURE_S4_TO_S2:
            if(Fan_IsRunning())
            {
                // DAC值上升
                uint16_t current_speed = fan_control.speed_dac;
                if(current_speed < FAN_DAC_MAX)
                {
                    uint16_t new_speed = current_speed + 409; // 增加10% (4095/10)
                    if(new_speed > FAN_DAC_MAX) new_speed = FAN_DAC_MAX;
                    Fan_SetSpeed(new_speed);
                }
            }
            break;

        case GESTURE_S3_TO_S1:
            if(Fan_IsRunning())
            {
                // DAC值下降
                uint16_t current_speed = fan_control.speed_dac;
                if(current_speed > 409)
                {
                    uint16_t new_speed = current_speed - 409; // 减少10%
                    Fan_SetSpeed(new_speed);
                }
            }
            break;
            
        default:
            break;
    }
    
    return STATE_FAN_CONTROL;
}

/**
 * @brief  处理时间设定状态
 * @param  gesture: 手势
 * @param  distance: 距离
 * @retval 新状态
 */
static SystemState_t StateMachine_ProcessTimeSetting(GestureType_t gesture, float distance)
{
    // 时间设定逻辑 (15-30s范围)
    // 距离5-20cm映射到时间15-30s
    if(distance >= 5.0f && distance <= 20.0f)
    {
       uint16_t time_setting = 15 + (uint16_t)((distance - 5.0f) / 15.0f * 15.0f);
        // TODO: 保存时间设定值
    }

    // 任何手势都返回待机状态
    if(gesture != GESTURE_NONE)
    {
        return STATE_IDLE;
    }

    return STATE_TIME_SETTING;
}

/**
 * @brief  处理电压设定状态
 * @param  gesture: 手势
 * @param  distance: 距离
 * @retval 新状态
 */
static SystemState_t StateMachine_ProcessVoltageSetting(GestureType_t gesture, float distance)
{
    // 速度设定逻辑 (0-4095 DAC范围)
    // 距离5-20cm映射到DAC值0-4095
    if(distance >= 5.0f && distance <= 20.0f)
    {
        uint16_t speed_setting = (uint16_t)((distance - 5.0f) / 15.0f * FAN_DAC_MAX);
        Fan_SetSpeed(speed_setting);
    }

    // 任何手势都返回待机状态
    if(gesture != GESTURE_NONE)
    {
        return STATE_IDLE;
    }

    return STATE_VOLTAGE_SETTING;
}

/**
 * @brief  处理组合操作设定状态
 * @param  gesture: 手势
 * @retval 新状态
 */
static SystemState_t StateMachine_ProcessComboSetting(GestureType_t gesture)
{
    // 组合操作设定逻辑
    // TODO: 实现组合操作记录功能

    // 任何手势都返回待机状态
    if(gesture != GESTURE_NONE)
    {
        return STATE_IDLE;
    }

    return STATE_COMBO_SETTING;
}

/**
 * @brief  处理组合操作运行状态
 * @param  None
 * @retval 新状态
 */
static SystemState_t StateMachine_ProcessComboRunning(void)
{
    // 组合操作运行逻辑
    // TODO: 实现组合操作执行功能

    // 运行完成后返回待机状态
    return STATE_IDLE;
}

/**
 * @brief  设置系统状态
 * @param  new_state: 新状态
 * @retval None
 */
void StateMachine_SetState(SystemState_t new_state)
{
    if(new_state != state_machine.current_state)
    {
        state_machine.last_state = state_machine.current_state;
        state_machine.current_state = new_state;
        state_machine.state_enter_time = HAL_GetTick();
        state_machine.state_changed = 1;
        
        // 设置状态超时
        switch(new_state)
        {
            case STATE_TIME_SETTING:
            case STATE_VOLTAGE_SETTING:
                state_machine.state_timeout = STATE_TIMEOUT_SETTING;
                break;
                
            case STATE_COMBO_SETTING:
            case STATE_COMBO_RUNNING:
                state_machine.state_timeout = STATE_TIMEOUT_COMBO;
                break;
                
            default:
                state_machine.state_timeout = 0;
                break;
        }
    }
}

/**
 * @brief  获取当前状态
 * @param  None
 * @retval 当前状态
 */
SystemState_t StateMachine_GetState(void)
{
    return state_machine.current_state;
}

/**
 * @brief  获取风扇状态
 * @param  None
 * @retval 风扇状态
 */
FanState_t StateMachine_GetFanState(void)
{
    return state_machine.fan_state;
}

/**
 * @brief  检查状态是否改变
 * @param  None
 * @retval 1-改变, 0-未改变
 */
uint8_t StateMachine_IsStateChanged(void)
{
    uint8_t changed = state_machine.state_changed;
    state_machine.state_changed = 0;  // 清除标志
    return changed;
}

/**
 * @brief  状态机更新
 * @param  None
 * @retval None
 */
void StateMachine_Update(void)
{
    uint32_t current_time = HAL_GetTick();
    
    // 检查状态超时
    if(state_machine.state_timeout > 0 && 
       (current_time - state_machine.state_enter_time > state_machine.state_timeout))
    {
        StateMachine_SetState(STATE_IDLE);
    }
}

/**
 * @brief  获取状态名称
 * @param  state: 状态
 * @retval 状态名称字符串
 */
const char* StateMachine_GetStateName(SystemState_t state)
{
    if(state < STATE_COUNT)
        return state_names[state];
    else
        return "UNKNOWN";
}
