#ifndef __INFO_H
#define __INFO_H
#include "sys.h"
#include "usart.h"

//////////////////////////////////////////////////////////////////////////////////
// 非接触式控制盘 - 串口屏信息发送模块
//
// 【功能说明】
// 负责向串口屏发送各种状态信息和控制命令
// 包括时间设定、电压设定、距离信息、风扇状态等
//
// 【通信协议】
// 串口屏使用特定的文本控件更新格式：
// 格式：控件名.txt="内容"
// 结束符：0xFF 0xFF 0xFF (三个字节)
//
// 【发送内容】
// t0_set_time.txt    - 设定运行时间
// t1_countdown.txt   - 倒计时显示
// t2_set_u.txt       - 设定工作电压
// t3_cmd_d.txt       - 操作距离
// t4_rpm.txt         - 风扇转速
// t5_direction.txt   - 风扇转向
// vis 10,1/0         - 运动状态图片显示
//////////////////////////////////////////////////////////////////////////////////

// 串口屏通信参数
#define INFO_UART_HANDLE    UART1_Handler   // 使用UART1与串口屏通信
#define INFO_END_CMD        {0xFF, 0xFF, 0xFF}  // 串口屏命令结束符
#define INFO_BUFFER_SIZE    100             // 发送缓冲区大小

// 风扇转向枚举
typedef enum {
    FAN_DIR_STOP = 0,       // 停止
    FAN_DIR_FORWARD,        // 正转
    FAN_DIR_REVERSE         // 反转
} InfoFanDirection_t;

// 运动状态枚举
typedef enum {
    MOTION_STOP = 0,        // 停止状态
    MOTION_RUNNING = 1      // 运行状态
} InfoMotionState_t;

// 全局变量声明
extern char info_buffer[INFO_BUFFER_SIZE];

// 函数声明
void Info_Init(void);                                   // 信息模块初始化
void Info_SendCommand(const char* command);            // 发送命令到串口屏
void Info_SendSetTime(uint16_t time_seconds);          // 发送设定运行时间
void Info_SendCountdown(uint16_t countdown_seconds);   // 发送倒计时
void Info_SendSetVoltage(float voltage);               // 发送设定工作电压
void Info_SendDistance(float distance);                // 发送操作距离
void Info_SendRPM(uint16_t rpm);                       // 发送风扇转速
void Info_SendDirection(InfoFanDirection_t direction); // 发送风扇转向
void Info_SendMotionState(InfoMotionState_t state);    // 发送运动状态
void Info_SendFinalDistance(int FinalDistance);   	   // 发送最终测定距离
void Info_UpdateAll(void);                             // 更新所有信息
void Info_SendTimeInfo(uint16_t set_time, uint16_t countdown); // 发送时间信息
void Info_SendFanInfo(uint8_t is_running, float voltage, uint16_t pwm_duty); // 发送风扇信息
void Info_Test(void);                               // 测试串口屏通信

#endif
