#ifndef __DISPLAY_UPDATE_H
#define __DISPLAY_UPDATE_H
#include "sys.h"
#include "lcd.h"
#include "../HARDWARE/ULTRASONIC/ultrasonic.h"
#include "../HARDWARE/FAN/fan.h"
#include "../APP/GESTURE/gesture.h"
#include "../APP/STATE_MACHINE/state_machine.h"

//////////////////////////////////////////////////////////////////////////////////	 
// 非接触式控制盘 - 显示更新模块
// 
// 【功能说明】
// 负责LCD屏幕的显示初始化和实时更新
// 包括距离显示、系统状态、风扇状态、光电门状态、命令状态、DAC输出等
//
// 【显示布局】
// - 标题区域：系统名称和功能描述
// - 数据区域：距离、状态、风扇、手势等信息
// - 状态区域：光电门状态、命令状态、DAC输出
// - 提示区域：系统运行状态提示
//
// 【更新策略】
// - 显示初始化：系统启动时调用一次，设置固定标签
// - 显示更新：主循环中定期调用，更新动态数据
// - 颜色编码：不同类型信息使用不同颜色区分
//////////////////////////////////////////////////////////////////////////////////

// 显示更新间隔定义
#define DISPLAY_UPDATE_INTERVAL 200  // 100ms更新一次显示

// 显示字符串缓冲区大小
#define DISPLAY_STR_SIZE 100

// 全局变量声明
extern char display_str[DISPLAY_STR_SIZE];
extern uint32_t last_display_update;


// 函数声明
void Display_Init(void);                        // 显示初始化
void Display_Update(void);                      // 显示更新
void Display_ShowDistance(float distance);     // 显示距离信息
void Display_ShowSystemState(SystemState_t state); // 显示系统状态
void Display_ShowFanStatus(void);              // 显示风扇状态
void Display_ShowGesture(GestureType_t gesture); // 显示手势信息
void Display_ShowPhotoSwitches(void);          // 显示光电门状态
void Display_ShowCommand(GestureType_t gesture); // 显示命令状态
void Display_ShowDAC(float dac_voltage, float distance); // 显示DAC输出

#endif
