# DAC风扇控制修改说明

## 修改概述

将原有的PWM控制风扇系统改为IO口控制正反转 + DAC控制转速的方案。

## 主要修改内容

### 1. 硬件引脚重新分配

**原方案 (PWM控制):**
- PA6: TIM3_CH1 PWM输出
- PA7: 风扇使能控制

**新方案 (IO+DAC控制):**
- PA6: 方向控制 (DIR) - 高电平=正转，低电平=反转
- PA5: 风扇使能控制 (可选)
- PA4: DAC_OUT1 - 转速控制 (0-3.3V对应0-最高速)

### 2. 控制逻辑修改

**转向控制:**
- 停止: DIR=0 (低电平，DAC=0停止转动)
- 正转: DIR=1 (高电平)
- 反转: DIR=0 (低电平)

**转速控制:**
- DAC值范围: 0-4095 (12位DAC)
- DAC=0: 风扇停止
- DAC=4095: 最高转速
- 等效电压: 0-3.3V

### 3. 数据结构修改

**FanControl_t结构体:**
```c
typedef struct {
    FanDirection_t direction;           // 转向
    uint16_t speed_dac;                // DAC转速控制值 0-4095
    uint16_t voltage_mv;               // 等效电压(mV) 0-3300
    uint8_t is_running;                // 运行状态
    uint32_t run_time_ms;              // 运行时间(ms)
    uint32_t start_time;               // 启动时间戳
    
    // 速度调节相关
    VoltageAdjustState_t speed_adjust_state;    // 速度调节状态
    uint16_t target_speed_dac;                  // 目标DAC值
    uint16_t start_speed_dac;                   // 起始DAC值
    uint32_t adjust_start_time;                 // 调节开始时间
    uint32_t adjust_duration_ms;                // 调节持续时间(ms)
} FanControl_t;
```

### 4. 函数接口修改

**新增函数:**
- `Fan_SetSpeed(uint16_t dac_value)` - 设置转速(DAC值)
- `Fan_SetDirection(FanDirection_t direction)` - 设置转向
- `Fan_GetSpeed(void)` - 获取当前转速DAC值
- `Fan_StartSpeedAdjust()` - 开始速度调节
- `Fan_StopSpeedAdjust()` - 停止速度调节
- `Fan_IsSpeedAdjusting()` - 检查是否正在调节速度
- `Fan_UpdateSpeedAdjust()` - 更新速度调节

**移除函数:**
- `Fan_SetVoltage()` - 改为内部计算等效电压
- `Fan_SetDuty()` - 不再使用PWM占空比
- `Fan_StartVoltageAdjust()` - 改名为速度调节
- `Fan_UpdateVoltageAdjust()` - 改名为速度调节

### 5. 参数定义修改

```c
// DAC转速控制参数
#define FAN_DAC_MIN         0       // 最小DAC值 (停止)
#define FAN_DAC_MAX         4095    // 最大DAC值 (最高速)
#define FAN_DAC_DEFAULT     1024    // 默认DAC值 (25%速度)
#define FAN_VOLTAGE_REF     3300    // DAC参考电压3.3V (mV)

// 速度调节参数
#define SPEED_ADJUST_DURATION_MS  5000    // 速度调节持续时间5秒
```

### 6. 主程序修改

**手势控制更新:**
- S1→S4: 速度上升 (0-4095，5秒渐变)
- S4→S1: 速度下降 (4095-0，5秒渐变)
- S1→S2: 风扇正转启动
- S2→S1: 风扇正转停止
- S3→S4: 风扇反转启动  
- S4→S3: 风扇反转停止

**显示更新:**
- 显示DAC值而不是PWM占空比
- 格式: "Running Forward (DAC: 1024)"

### 7. 状态机修改

**风扇控制状态:**
- 速度调节改为DAC值调节
- 每次调节增减10% (409个DAC单位)

**电压设定状态:**
- 改为速度设定状态
- 距离5-20cm映射到DAC值0-4095

## 测试程序

创建了 `TEST/dac_fan_control_test.c` 测试程序，包含:

1. **基本功能测试:**
   - 风扇初始化
   - 正转/反转启动
   - 停止功能

2. **速度调节测试:**
   - 渐变速度上升 (25%→100%)
   - 渐变速度下降 (100%→0%)

3. **方向控制测试:**
   - IO口状态验证
   - 方向切换测试

4. **手动速度设置测试:**
   - 不同速度档位测试 (12.5%, 25%, 50%, 75%, 100%)

## 使用说明

### 初始化
```c
Fan_Init();  // 初始化DAC和IO口
```

### 基本控制
```c
Fan_StartForward();     // 正转启动 (默认25%速度)
Fan_StartReverse();     // 反转启动 (默认25%速度)
Fan_Stop();             // 停止 (DAC=0)
```

### 速度控制
```c
Fan_SetSpeed(2048);     // 设置50%速度
Fan_SetSpeed(4095);     // 设置100%速度
Fan_SetSpeed(0);        // 停止
```

### 渐变调节
```c
Fan_StartSpeedAdjust(VOLTAGE_ADJUST_UP);    // 开始速度上升
Fan_StartSpeedAdjust(VOLTAGE_ADJUST_DOWN);  // 开始速度下降
```

### 状态查询
```c
uint16_t speed = Fan_GetSpeed();        // 获取当前DAC值
uint16_t voltage = Fan_GetVoltage();    // 获取等效电压(mV)
FanDirection_t dir = Fan_GetDirection(); // 获取转向
uint8_t running = Fan_IsRunning();      // 检查运行状态
```

## 注意事项

1. **DAC配置:** 确保DAC模块正确初始化，使用DAC_CHANNEL_1
2. **IO口配置:** DIR引脚控制正反转方向
3. **速度范围:** DAC值0表示停止，非0值表示运行
4. **渐变调节:** 调节过程中需要定期调用Fan_Update()
5. **硬件连接:** 确保电机驱动电路支持单引脚方向控制

## 兼容性

修改后的接口保持了大部分原有功能，主要变化:
- 速度控制从PWM占空比改为DAC值
- 增加了方向控制功能
- 保持了渐变调节特性
- 显示信息更新为DAC值
