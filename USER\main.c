/************************************************
 STM32F407 非接触式控制盘主程序
 基于ALIENTEK 探索者STM32F407开发板

 【系统功能概述】
 本系统实现了基于手势识别的非接触式风扇控制，主要功能包括：
 1. 手势识别：通过4个光电开关检测挥手动作，识别8种手势组合
 2. 距离测量：超声波传感器测量5-30cm范围内的距离
 3. 风扇控制：PWM调速、正反转控制、启停控制
 4. 智能设定：基于距离的时间和电压设定功能
 5. 组合操作：可存储和执行复杂的操作序列

 【手势识别原理】
 - S1→S2: 依次触发光电开关S1和S2，实现风扇正转启动
 - S2→S1: 依次触发光电开关S2和S1，实现风扇正转停止
 - S3→S4: 依次触发光电开关S3和S4，实现风扇反转启动
 - S4→S3: 依次触发光电开关S4和S3，实现风扇反转停止
 - S4→S2: 运行时电压上升手势
 - S3→S1: 运行时电压下降手势
 - S3→S2: 距离5-20cm时的时间设定手势
 - S4→S1: 距离5-20cm时的电压设定手势

 【硬件连接】
 - 光电开关S1-S4: PC0-PC3 (漫反射式，低电平触发)
 - 超声波测距S5: PD3(TRIG), PD6(ECHO) (HC-SR04)
 - 风扇PWM: PA6 (TIM3_CH1, 1kHz频率)
 - 风扇方向: PA7, PG7 (H桥方向控制)
 - 风扇使能: PG8 (H桥使能控制)
 - LCD显示: 开发板自带TFTLCD

 技术支持：www.openedv.com
************************************************/


#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "led.h"
#include "lcd.h"
#include "usmart.h"

// 硬件模块
#include "../HARDWARE/ULTRASONIC/ultrasonic.h" // 超声波测距
#include "../HARDWARE/PHOTOSWITCH/photoswitch.h" // 光电门检测
#include "../HARDWARE/FAN/fan.h" // 风扇控制
#include "../HARDWARE/DAC/dac.h"

// 应用模块
#include "../APP/GESTURE/gesture.h" // 手势识别
#include "../APP/STATE_MACHINE/state_machine.h" // 状态机
#include "../APP/DISPLAY_UPDATE/display_update.h" // lcd屏幕跟新
#include "../APP/INFO/info.h" // 向发送串口屏信息

// 全局变量
float distance = 0;
GestureType_t current_gesture = GESTURE_NONE;
SystemState_t system_state = STATE_IDLE;
float dac_voltage = 0;  // DAC输出电压

// 距离稳定性检测相关变量
#define STABILITY_TIME_MS       2000    // 稳定时间要求：2秒
uint16_t current_rounded_distance = 0;  // 当前四舍五入后的距离
uint16_t last_stable_distance = 0;      // 上次稳定的距离值
uint32_t stability_start_time = 0;      // 稳定性检测开始时间
uint8_t is_distance_stable = 0;         // 距离是否稳定标志
uint16_t final_d = 0;                   // 最终确定的距离值
uint16_t final_d_ready = 0;				// 是否有新的与距离相关命令标志位

// 距离到时间查找表 [距离cm][时间秒]
#define DISTANCE_TIME_TABLE_SIZE    26  // 5-30cm，共26个数据点
const uint16_t distance_time_table[DISTANCE_TIME_TABLE_SIZE][2] = {
    {5,  10},   {6,  12},   {7,  14},   {8,  16},   {9,  18},   // 5-9cm
    {10, 20},   {11, 22},   {12, 24},   {13, 26},   {14, 28},   // 10-14cm
    {15, 30},   {16, 32},   {17, 34},   {18, 36},   {19, 38},   // 15-19cm
    {20, 40},   {21, 42},   {22, 44},   {23, 46},   {24, 48},   // 20-24cm
    {25, 50},   {26, 52},   {27, 54},   {28, 56},   {29, 58},   // 25-29cm
    {30, 60}                                                     // 30cm
};

// 距离到电压查找表 [距离cm][电压*10(便于存储)]
#define DISTANCE_VOLTAGE_TABLE_SIZE 26  // 5-30cm，共26个数据点
const uint16_t distance_voltage_table[DISTANCE_VOLTAGE_TABLE_SIZE][2] = {
    {5,  30},   {6,  34},   {7,  38},   {8,  42},   {9,  46},   // 5-9cm -> 3.0-4.6V
    {10, 50},   {11, 54},   {12, 58},   {13, 62},   {14, 66},   // 10-14cm -> 5.0-6.6V
    {15, 70},   {16, 74},   {17, 78},   {18, 82},   {19, 86},   // 15-19cm -> 7.0-8.6V
    {20, 90},   {21, 92},   {22, 94},   {23, 96},   {24, 98},   // 20-24cm -> 9.0-9.8V
    {25, 100},  {26, 100},  {27, 100},  {28, 100},  {29, 100},  // 25-29cm -> 10.0V
    {30, 100}                                                    // 30cm -> 10.0V
};

// 外部变量声明
extern FanControl_t fan_control;
extern int rounded_distance;

/**
 * @brief  检测距离稳定性并更新final_d
 * @param  dist: 当前测量的距离值(cm)
 * @retval None
 */
void Distance_CheckStability(float dist)
{
    uint32_t current_time = HAL_GetTick();

    // 四舍五入当前距离
    current_rounded_distance = (uint16_t)(dist + 0.5f);

    // 检查距离是否发生变化
    if(current_rounded_distance != last_stable_distance) {
        // 距离发生变化，重新开始稳定性检测
        last_stable_distance = current_rounded_distance;
        stability_start_time = current_time;

        is_distance_stable = 0;
    } else {
        // 距离保持不变，检查是否已稳定足够时间
        if((current_time - stability_start_time) >= STABILITY_TIME_MS) {
            if(!is_distance_stable) {
                // 首次达到稳定状态
                is_distance_stable = 1;
				// 有新的与距离相关命令，需要后续处理
				final_d_ready = 1;
				
                final_d = current_rounded_distance;
		
                // 发送最终距离到串口屏
                Info_SendFinalDistance(final_d);
            }else{
				final_d_ready = 0;
			}
        }else{
			final_d_ready = 0;
		}
    }
}

/**
 * @brief  获取最终确定的距离值
 * @param  None
 * @retval 最终距离值(cm)
 */
uint16_t Distance_GetFinalDistance(void)
{
    return final_d;
}

/**
 * @brief  获取距离稳定状态
 * @param  None
 * @retval 1-稳定，0-不稳定
 */
uint8_t Distance_IsStable(void)
{
    return is_distance_stable;
}

/**
 * @brief  根据距离查找运行时间（查表法）
 * @param  distance: 距离值(cm)
 * @retval 运行时间(秒)
 */
uint16_t Distance_LookupTime(uint16_t distance)
{
    uint16_t i;

    // 在查找表中搜索匹配的距离
    for(i = 0; i < DISTANCE_TIME_TABLE_SIZE; i++) {
        if(distance_time_table[i][0] == distance) {
            return distance_time_table[i][1];  // 返回对应的时间
        }
    }

    // 如果没有精确匹配，返回最接近的值
    if(distance < 5) {
        return distance_time_table[0][1];  // 返回最小时间(10秒)
    } else if(distance > 30) {
        return distance_time_table[DISTANCE_TIME_TABLE_SIZE-1][1];  // 返回最大时间(60秒)
    } else {
        // 在范围内但没有精确匹配，返回最接近的较小值
        for(i = 0; i < DISTANCE_TIME_TABLE_SIZE-1; i++) {
            if(distance > distance_time_table[i][0] && distance < distance_time_table[i+1][0]) {
                return distance_time_table[i][1];  // 返回较小距离对应的时间
            }
        }
        return distance_time_table[DISTANCE_TIME_TABLE_SIZE-1][1];  // 默认返回最大时间
    }
}

/**
 * @brief  根据距离查找电压（查表法）
 * @param  distance: 距离值(cm)
 * @retval 电压值(V)
 */
float Distance_LookupVoltage(uint16_t distance)
{
    uint16_t i;

    // 在查找表中搜索匹配的距离
    for(i = 0; i < DISTANCE_VOLTAGE_TABLE_SIZE; i++) {
        if(distance_voltage_table[i][0] == distance) {
            return (float)distance_voltage_table[i][1] / 10.0f;  // 返回对应的电压(除以10)
        }
    }

    // 如果没有精确匹配，返回最接近的值
    if(distance < 5) {
        return (float)distance_voltage_table[0][1] / 10.0f;  // 返回最小电压(3.0V)
    } else if(distance > 30) {
        return (float)distance_voltage_table[DISTANCE_VOLTAGE_TABLE_SIZE-1][1] / 10.0f;  // 返回最大电压(10.0V)
    } else {
        // 在范围内但没有精确匹配，返回最接近的较小值
        for(i = 0; i < DISTANCE_VOLTAGE_TABLE_SIZE-1; i++) {
            if(distance > distance_voltage_table[i][0] && distance < distance_voltage_table[i+1][0]) {
                return (float)distance_voltage_table[i][1] / 10.0f;  // 返回较小距离对应的电压
            }
        }
        return (float)distance_voltage_table[DISTANCE_VOLTAGE_TABLE_SIZE-1][1] / 10.0f;  // 默认返回最大电压
    }
}

// 函数声明
void System_Init(void);
void Control_Process(void);
void Control_ProcessTimeSetting(void);
void Control_ProcessVoltageSetting(void);
void Control_ProcessComboSetting(void);
void Control_ProcessComboRunning(void);

/**
 * @brief  主函数
 * @param  None
 * @retval None
 */
int main(void)
{
    // 系统初始化
    System_Init();
    
    // 显示初始化函数
    Display_Init();

    // 主循环体- 系统核心控制逻辑
    // 采用轮询方式，以10ms为周期执行各个功能模块
    while(1)
    {
        // 【1. 超声波数据采集】
        // 获取超声波传感器数据，测量距离相关的功能
        distance = Ultrasonic_GetDistance();

        // 检测距离稳定性并更新final_d
        Distance_CheckStability(distance);

        // 【2. 光电设备扫描】
        // 扫描4个光电开关的状态变化，为手势识别提供原始数据
        PhotoSwitch_Scan();

        // 【3. 手势识别处理】
        // 能够识别到的手势类型：如S1→S2、S3→S4等
        current_gesture = Gesture_Recognize();

        // 【5.1 基本风扇控制处理】
        // 处理风扇正转、反转和停止控制
        if(current_gesture != GESTURE_NONE)
        {
            switch(current_gesture)
            {
                case GESTURE_S1_TO_S2:
                    // S1→S2手势：风扇正转启动
                    if(!Fan_IsRunning())
                    {
                        Fan_StartForward();
                    }
                    else if(Fan_GetDirection() != FAN_FORWARD)
                    {
                        // 如果当前是反转，切换到正转
                        Fan_StartForward();
                    }
                    break;

                case GESTURE_S2_TO_S1:
                    // S2→S1手势：风扇停止（仅当正转时）
                    if(Fan_IsRunning() && Fan_GetDirection() == FAN_FORWARD)
                    {
                        Fan_Stop();
                    }
                    break;

                case GESTURE_S3_TO_S4:
                    // S3→S4手势：风扇反转启动
                    if(!Fan_IsRunning())
                    {
                        Fan_StartReverse();
                    }
                    else if(Fan_GetDirection() != FAN_REVERSE)
                    {
                        // 如果当前是正转，切换到反转
                        Fan_StartReverse();
                    }
                    break;

                case GESTURE_S4_TO_S3:
                    // S4→S3手势：风扇停止（仅当反转时）
                    if(Fan_IsRunning() && Fan_GetDirection() == FAN_REVERSE)
                    {
                        Fan_Stop();
                    }
                    break;

                default:
                    break;
            }
        }

        // 【5.2 风扇电压调节处理】
        // 在风扇运行时检测特定手势并执行电压调节
        // 注意：使用不同的手势组合避免与启停控制冲突
        // 使用查找表方式进行精确的电压级别调节（3V-10V，每0.5V一级）
        if(Fan_IsRunning() && current_gesture != GESTURE_NONE)
        {
            if(current_gesture == GESTURE_S1_TO_S4)
            {
                // S1→S4手势：电压上升（3V→10V，5秒，共15级调节）
                if(!Fan_IsSpeedAdjusting())
                {
                    Fan_StartVoltageAdjust(VOLTAGE_ADJUST_UP);
                }
            }
            else if(current_gesture == GESTURE_S4_TO_S1)
            {
                // S4→S1手势：电压下降（10V→3V，5秒，共15级调节）
                if(!Fan_IsSpeedAdjusting())
                {
                    Fan_StartVoltageAdjust(VOLTAGE_ADJUST_DOWN);
                }
            }
        }
		
        // 【6. 与距离相关命令的处理】
		if(final_d_ready){

			final_d_ready = 0;

			if(Fan_IsRunning() && current_gesture != GESTURE_NONE)
			{
				if(current_gesture == GESTURE_S3_TO_S2) // 按距离final_d设定运行时间t
				{
					// 根据final_d设定运行时间
					uint16_t run_time_seconds = Distance_MapToTime(final_d);

					// 发送设定时间到串口屏
					Info_SendSetTime(run_time_seconds);

					// 显示调试信息（可选）
					// char debug_msg[50];
					// sprintf(debug_msg, "Time set: %dcm -> %ds", final_d, run_time_seconds);
				}
				else if(current_gesture == GESTURE_S4_TO_S1) // 按距离final_d设定风扇电压使用查表法
				{
					// 根据final_d设定风扇电压
					float target_voltage = Distance_MapToVoltage(final_d);

					// 使用风扇电压设定函数
					Fan_SetSpeedByVoltage((uint16_t)(target_voltage * 1000));  // 转换为mV

					// 发送设定电压到串口屏
					Info_SendSetVoltage(target_voltage);

					// 显示调试信息（可选）
					// char debug_msg[50];
					// sprintf(debug_msg, "Voltage set: %dcm -> %.1fV", final_d, target_voltage);
				}
			}
		}
		

        // 【7. 状态机逻辑处理】
        // 根据当前状态和识别到的手势，执行相应的控制逻辑
        // 包括状态转换、风扇控制、参数设定等
        // 注意：暂时注释掉状态机处理，只保留基本功能
        // StateMachine_Process(current_gesture);

        // 【8. 控制逻辑处理】
        // 根据当前系统状态，执行特定的控制任务
        // 注意：暂时注释掉复杂控制逻辑，只保留基本功能
        // Control_Process();

        // 【9. 显示界面更新】
        // 每100ms更新一次LCD显示，避免频繁刷新影响性能
        if(HAL_GetTick() - last_display_update > DISPLAY_UPDATE_INTERVAL)
        {
			float actual_voltage = 0;
			InfoFanDirection_t info_direction;
			
            // 只保留距离显示功能
            Display_ShowDistance(distance);

            // 显示风扇状态（电压调节时需要）
            Display_ShowFanStatus();

            // 显示光电门状态（调试用）
            Display_ShowPhotoSwitches();
			
			// 串口屏数据发送
			Info_SendDistance(distance);                    // 发送距离信息

			// 发送风扇信息（使用查找表获取实际电压和转速）
			actual_voltage = Fan_GetVoltage() / 1000.0f;  // 转换为V
			

			// 转换风扇方向枚举
			if(Fan_IsRunning()) {
				info_direction = (Fan_GetDirection() == FAN_FORWARD) ? FAN_DIR_FORWARD : FAN_DIR_REVERSE;
			} else {
				info_direction = FAN_DIR_STOP;
			}

			// 使用新的查找表函数发送风扇信息
			Info_SendFanInfoWithLookup(Fan_IsRunning(), actual_voltage, info_direction);
			
            last_display_update = HAL_GetTick();
        }

        // 【10. 系统维护任务】
        // 状态机超时检查和自动状态管理
        // 注释掉状态机更新，只保留基本功能
        // StateMachine_Update();

        // 风扇运行状态更新和保护逻辑
        Fan_Update();

        // 【10. 状态指示】
        // LED指示灯显示系统运行状态
        if(Fan_IsRunning())
        {
            LED0 = !LED0;  // 风扇运行时LED闪烁，提供视觉反馈
        }
        else
        {
            LED0 = 1;      // 风扇停止时LED熄灭
        }

        delay_ms(10);
    }
}

/**
 * @brief  系统初始化
 * @param  None
 * @retval None
 */
void System_Init(void)
{
    HAL_Init();                     // 初始化HAL库
    Stm32_Clock_Init(336,8,2,7);    // 设置时钟,168Mhz
    delay_init(168);                // 延时初始化
    uart_init(115200);              // 串口初始化
    LED_Init();                     // LED初始化
    LCD_Init();                     // LCD初始化
    usmart_dev.init(84);            // 初始化USMART

    // 硬件模块初始化
    Ultrasonic_Init();              // 超声波初始化
    PhotoSwitch_Init();             // 光电开关初始化
    Fan_Init();                     // 风扇控制初始化
    DAC_Init();                     // DAC初始化

    // 应用模块初始化
    Gesture_Init();                 // 手势识别初始化
    StateMachine_Init();            // 状态机初始化
    Info_Init();                    // 串口屏信息模块初始化

    // 显示模块初始化
    Display_Init();                 // 显示更新初始化
}

/**
 * @brief  控制处理
 * @param  None
 * @retval None
 */
void Control_Process(void)
{
    // 根据当前状态执行相应的控制逻辑
    switch(system_state)
    {
        case STATE_IDLE:
            // 待机状态，无特殊处理
            break;
            
        case STATE_FAN_CONTROL:
            // 风扇控制状态，已在状态机中处理
            break;
            
        case STATE_TIME_SETTING:
            // 时间设定状态处理
            Control_ProcessTimeSetting();
            break;
            
        case STATE_VOLTAGE_SETTING:
            // 电压设定状态处理
            Control_ProcessVoltageSetting();
            break;
            
        case STATE_COMBO_SETTING:
            // 组合操作设定状态处理
            Control_ProcessComboSetting();
            break;
            
        case STATE_COMBO_RUNNING:
            // 组合操作运行状态处理
            Control_ProcessComboRunning();
            break;
            
        default:
            break;
    }
}

// USMART调试函数
void led_set(u8 sta)
{
    LED0 = sta;
}

void test_fun(void)
{
    printf("System Status:\r\n");
    printf("Distance: %.1f cm\r\n", distance);
    printf("State: %s\r\n", StateMachine_GetStateName(system_state));
    printf("Fan: %s\r\n", Fan_IsRunning() ? "Running" : "Stopped");
    printf("Voltage: %.1fV\r\n", Fan_GetVoltage() / 1000.0f);
}

/**
 * @brief  时间设定状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessTimeSetting(void)
{
    // 时间设定状态的控制逻辑
    // 在状态机中已处理主要逻辑，这里可以添加额外的显示或处理
}

/**
 * @brief  电压设定状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessVoltageSetting(void)
{
    // 电压设定状态的控制逻辑
    // 在状态机中已处理主要逻辑，这里可以添加额外的显示或处理
}

/**
 * @brief  组合操作设定状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessComboSetting(void)
{
    // 组合操作设定状态的控制逻辑
    // TODO: 实现组合操作的用户界面和存储逻辑
}

/**
 * @brief  组合操作运行状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessComboRunning(void)
{
    // 组合操作运行状态的控制逻辑
    // TODO: 实现组合操作的执行逻辑
}


/**
 * @brief  DAC控制风扇转速
 * @param  None
 * @retval None
 */
void DAC_Control_fen(void){
	// 【1.1 DAC输出控制】
	// 根据测量距离控制DAC输出电压
	if(Ultrasonic_IsInRange(distance))
	{
		// 距离范围5-20cm映射到DAC电压2.888 - 0.825V（对应的放大后驱动风扇的电压为10.5 - 3.0v）
		// 距离越近，电压越高
		dac_voltage = 2.888f - ((distance - 5.0f) / 15.0f) * 2.888f;
		if(dac_voltage > 2.888f) dac_voltage = 2.888f;
		if(dac_voltage < 0.825f) dac_voltage = 0.825f;
		DAC_SetVoltage(dac_voltage);
	}
	else
	{
		// 超出范围时DAC输出0V
		dac_voltage = 0;
		DAC_SetVoltage(0);
	}
}
