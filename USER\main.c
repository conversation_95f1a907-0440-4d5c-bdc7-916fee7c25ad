/************************************************
 STM32F407 非接触式控制盘主程序
 基于ALIENTEK 探索者STM32F407开发板

 【系统功能概述】
 本系统实现了基于手势识别的非接触式风扇控制，主要功能包括：
 1. 手势识别：通过4个光电开关检测挥手动作，识别8种手势组合
 2. 距离测量：超声波传感器测量5-30cm范围内的距离
 3. 风扇控制：PWM调速、正反转控制、启停控制
 4. 智能设定：基于距离的时间和电压设定功能
 5. 组合操作：可存储和执行复杂的操作序列

 【手势识别原理】
 - S1→S2: 依次触发光电开关S1和S2，实现风扇正转启动
 - S2→S1: 依次触发光电开关S2和S1，实现风扇正转停止
 - S3→S4: 依次触发光电开关S3和S4，实现风扇反转启动
 - S4→S3: 依次触发光电开关S4和S3，实现风扇反转停止
 - S4→S2: 运行时电压上升手势
 - S3→S1: 运行时电压下降手势
 - S3→S2: 距离5-20cm时的时间设定手势
 - S4→S1: 距离5-20cm时的电压设定手势

 【硬件连接】
 - 光电开关S1-S4: PC0-PC3 (漫反射式，低电平触发)
 - 超声波测距S5: PD3(TRIG), PD6(ECHO) (HC-SR04)
 - 风扇PWM: PA6 (TIM3_CH1, 1kHz频率)
 - 风扇方向: PA7, PG7 (H桥方向控制)
 - 风扇使能: PG8 (H桥使能控制)
 - LCD显示: 开发板自带TFTLCD

 技术支持：www.openedv.com
************************************************/


#include "sys.h"
#include "delay.h"
#include "usart.h"
#include "led.h"
#include "lcd.h"
#include "usmart.h"

// 硬件模块
#include "../HARDWARE/ULTRASONIC/ultrasonic.h" // 超声波测距
#include "../HARDWARE/PHOTOSWITCH/photoswitch.h" // 光电门检测
#include "../HARDWARE/FAN/fan.h" // 风扇控制
#include "../HARDWARE/DAC/dac.h"

// 应用模块
#include "../APP/GESTURE/gesture.h" // 手势识别
#include "../APP/STATE_MACHINE/state_machine.h" // 状态机
#include "../APP/DISPLAY_UPDATE/display_update.h" // lcd屏幕跟新
#include "../APP/INFO/info.h" // 向发送串口屏信息

// 全局变量
float distance = 0;
GestureType_t current_gesture = GESTURE_NONE;
SystemState_t system_state = STATE_IDLE;
float dac_voltage = 0;  // DAC输出电压

// 外部变量声明
extern FanControl_t fan_control;
extern int rounded_distance;

// 函数声明
void System_Init(void);
void Control_Process(void);
void Control_ProcessTimeSetting(void);
void Control_ProcessVoltageSetting(void);
void Control_ProcessComboSetting(void);
void Control_ProcessComboRunning(void);

/**
 * @brief  主函数
 * @param  None
 * @retval None
 */
int main(void)
{
    // 系统初始化
    System_Init();
    
    // 显示初始化函数
    Display_Init();

    // 主循环体- 系统核心控制逻辑
    // 采用轮询方式，以10ms为周期执行各个功能模块
    while(1)
    {
        // 【1. 超声波数据采集】
        // 获取超声波传感器数据，测量距离相关的功能
        distance = Ultrasonic_GetDistance();

        // 【2. 光电设备扫描】
        // 扫描4个光电开关的状态变化，为手势识别提供原始数据
        PhotoSwitch_Scan();

        // 【3. 手势识别处理】
        // 基于光电开关状态变化，识别用户的手势操作
        // 能够识别到的手势类型：如S1→S2、S3→S4等
        // 内部已包含滑动逻辑检测（时间间隔和超时检测）
        current_gesture = Gesture_Recognize();

        // 【5. 基本风扇控制处理】
        // 处理风扇正转、反转和停止控制
        if(current_gesture != GESTURE_NONE)
        {
            switch(current_gesture)
            {
                case GESTURE_S1_TO_S2:
                    // S1→S2手势：风扇正转启动
                    if(!Fan_IsRunning())
                    {
                        Fan_StartForward();
                    }
                    else if(Fan_GetDirection() != FAN_FORWARD)
                    {
                        // 如果当前是反转，切换到正转
                        Fan_StartForward();
                    }
                    break;

                case GESTURE_S2_TO_S1:
                    // S2→S1手势：风扇停止（仅当正转时）
                    if(Fan_IsRunning() && Fan_GetDirection() == FAN_FORWARD)
                    {
                        Fan_Stop();
                    }
                    break;

                case GESTURE_S3_TO_S4:
                    // S3→S4手势：风扇反转启动
                    if(!Fan_IsRunning())
                    {
                        Fan_StartReverse();
                    }
                    else if(Fan_GetDirection() != FAN_REVERSE)
                    {
                        // 如果当前是正转，切换到反转
                        Fan_StartReverse();
                    }
                    break;

                case GESTURE_S4_TO_S3:
                    // S4→S3手势：风扇停止（仅当反转时）
                    if(Fan_IsRunning() && Fan_GetDirection() == FAN_REVERSE)
                    {
                        Fan_Stop();
                    }
                    break;

                default:
                    break;
            }
        }

        // 【6. 风扇电压调节处理】
        // 在风扇运行时检测特定手势并执行电压调节
        // 注意：使用不同的手势组合避免与启停控制冲突
        if(Fan_IsRunning() && current_gesture != GESTURE_NONE)
        {
            if(current_gesture == GESTURE_S1_TO_S4)
            {
                // S1→S4手势：速度上升（0-4095，5秒）
                if(!Fan_IsSpeedAdjusting())
                {
                    Fan_StartSpeedAdjust(VOLTAGE_ADJUST_UP);
                }
            }
            else if(current_gesture == GESTURE_S4_TO_S1)
            {
                // S4→S1手势：速度下降（4095-0，5秒）
                if(!Fan_IsSpeedAdjusting())
                {
                    Fan_StartSpeedAdjust(VOLTAGE_ADJUST_DOWN);
                }
            }
        }

        // 【7. 状态机逻辑处理】
        // 根据当前状态和识别到的手势，执行相应的控制逻辑
        // 包括状态转换、风扇控制、参数设定等
        // 注意：暂时注释掉状态机处理，只保留基本功能
        // StateMachine_Process(current_gesture);

        // 【8. 控制逻辑处理】
        // 根据当前系统状态，执行特定的控制任务
        // 注意：暂时注释掉复杂控制逻辑，只保留基本功能
        // Control_Process();

        // 【9. 显示界面更新】
        // 每100ms更新一次LCD显示，避免频繁刷新影响性能
        if(HAL_GetTick() - last_display_update > DISPLAY_UPDATE_INTERVAL)
        {
            // 只保留距离显示功能
            Display_ShowDistance(distance);

            // 显示风扇状态（电压调节时需要）
            Display_ShowFanStatus();

            // 显示光电门状态（调试用）
            Display_ShowPhotoSwitches();
			
			// 串口屏数据发送
			Info_SendDistance(distance);                    // 发送距离信息
			
			Info_SendSetVoltage(Fan_GetVoltage()/1000.0f); // 发送电压信息
			
			if(Fan_IsRunning()) {
				Info_SendDirection(Fan_GetDirection() == FAN_FORWARD ? FAN_DIR_FORWARD : FAN_DIR_REVERSE);
				Info_SendMotionState(MOTION_RUNNING);
			} else {
				Info_SendMotionState(MOTION_STOP);
			}
			
            last_display_update = HAL_GetTick();
        }

        // 【10. 系统维护任务】
        // 状态机超时检查和自动状态管理
        // 注释掉状态机更新，只保留基本功能
        // StateMachine_Update();

        // 风扇运行状态更新和保护逻辑
        Fan_Update();

        // 【10. 状态指示】
        // LED指示灯显示系统运行状态
        if(Fan_IsRunning())
        {
            LED0 = !LED0;  // 风扇运行时LED闪烁，提供视觉反馈
        }
        else
        {
            LED0 = 1;      // 风扇停止时LED熄灭
        }

        delay_ms(10);
    }
}

/**
 * @brief  系统初始化
 * @param  None
 * @retval None
 */
void System_Init(void)
{
    HAL_Init();                     // 初始化HAL库
    Stm32_Clock_Init(336,8,2,7);    // 设置时钟,168Mhz
    delay_init(168);                // 延时初始化
    uart_init(115200);              // 串口初始化
    LED_Init();                     // LED初始化
    LCD_Init();                     // LCD初始化
    usmart_dev.init(84);            // 初始化USMART

    // 硬件模块初始化
    Ultrasonic_Init();              // 超声波初始化
    PhotoSwitch_Init();             // 光电开关初始化
    Fan_Init();                     // 风扇控制初始化
    DAC_Init();                     // DAC初始化

    // 应用模块初始化
    Gesture_Init();                 // 手势识别初始化
    StateMachine_Init();            // 状态机初始化
    Info_Init();                    // 串口屏信息模块初始化

    // 显示模块初始化
    Display_Init();                 // 显示更新初始化
}

/**
 * @brief  控制处理
 * @param  None
 * @retval None
 */
void Control_Process(void)
{
    // 根据当前状态执行相应的控制逻辑
    switch(system_state)
    {
        case STATE_IDLE:
            // 待机状态，无特殊处理
            break;
            
        case STATE_FAN_CONTROL:
            // 风扇控制状态，已在状态机中处理
            break;
            
        case STATE_TIME_SETTING:
            // 时间设定状态处理
            Control_ProcessTimeSetting();
            break;
            
        case STATE_VOLTAGE_SETTING:
            // 电压设定状态处理
            Control_ProcessVoltageSetting();
            break;
            
        case STATE_COMBO_SETTING:
            // 组合操作设定状态处理
            Control_ProcessComboSetting();
            break;
            
        case STATE_COMBO_RUNNING:
            // 组合操作运行状态处理
            Control_ProcessComboRunning();
            break;
            
        default:
            break;
    }
}

// USMART调试函数
void led_set(u8 sta)
{
    LED0 = sta;
}

void test_fun(void)
{
    printf("System Status:\r\n");
    printf("Distance: %.1f cm\r\n", distance);
    printf("State: %s\r\n", StateMachine_GetStateName(system_state));
    printf("Fan: %s\r\n", Fan_IsRunning() ? "Running" : "Stopped");
    printf("Voltage: %.1fV\r\n", Fan_GetVoltage() / 1000.0f);
}

/**
 * @brief  时间设定状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessTimeSetting(void)
{
    // 时间设定状态的控制逻辑
    // 在状态机中已处理主要逻辑，这里可以添加额外的显示或处理
}

/**
 * @brief  电压设定状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessVoltageSetting(void)
{
    // 电压设定状态的控制逻辑
    // 在状态机中已处理主要逻辑，这里可以添加额外的显示或处理
}

/**
 * @brief  组合操作设定状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessComboSetting(void)
{
    // 组合操作设定状态的控制逻辑
    // TODO: 实现组合操作的用户界面和存储逻辑
}

/**
 * @brief  组合操作运行状态处理
 * @param  None
 * @retval None
 */
void Control_ProcessComboRunning(void)
{
    // 组合操作运行状态的控制逻辑
    // TODO: 实现组合操作的执行逻辑
}


/**
 * @brief  DAC控制风扇转速
 * @param  None
 * @retval None
 */
void DAC_Control_fen(void){
	// 【1.1 DAC输出控制】
	// 根据测量距离控制DAC输出电压
	if(Ultrasonic_IsInRange(distance))
	{
		// 距离范围5-20cm映射到DAC电压2.888 - 0.825V（对应的放大后驱动风扇的电压为10.5 - 3.0v）
		// 距离越近，电压越高
		dac_voltage = 2.888f - ((distance - 5.0f) / 15.0f) * 2.888f;
		if(dac_voltage > 2.888f) dac_voltage = 2.888f;
		if(dac_voltage < 0.825f) dac_voltage = 0.825f;
		DAC_SetVoltage(dac_voltage);
	}
	else
	{
		// 超出范围时DAC输出0V
		dac_voltage = 0;
		DAC_SetVoltage(0);
	}
}
